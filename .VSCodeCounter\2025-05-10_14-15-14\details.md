# Details

Date : 2025-05-10 14:15:14

Directory c:\\PFE_PROJECT\\ReactJs\\elite

Total : 77 files,  47120 codes, 1391 comments, 4627 blanks, all 53138 lines

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)

## Files
| filename | language | code | comment | blank | total |
| :--- | :--- | ---: | ---: | ---: | ---: |
| [Messages.js](/Messages.js) | JavaScript | 78 | 0 | 11 | 89 |
| [README.md](/README.md) | Markdown | 38 | 0 | 33 | 71 |
| [backend/.env](/backend/.env) | Properties | 3 | 0 | 1 | 4 |
| [backend/README.md](/backend/README.md) | Markdown | 62 | 0 | 23 | 85 |
| [backend/ai\_quick\_replies.log](/backend/ai_quick_replies.log) | log | 0 | 0 | 1 | 1 |
| [backend/app.py](/backend/app.py) | Python | 1,702 | 267 | 445 | 2,414 |
| [backend/category\_routes.py](/backend/category_routes.py) | Python | 296 | 40 | 81 | 417 |
| [backend/file\_upload.py](/backend/file_upload.py) | Python | 152 | 35 | 47 | 234 |
| [backend/file\_utils.py](/backend/file_utils.py) | Python | 80 | 15 | 23 | 118 |
| [backend/models.py](/backend/models.py) | Python | 33 | 0 | 3 | 36 |
| [backend/quick\_reply\_model.log](/backend/quick_reply_model.log) | log | 485 | 0 | 31 | 516 |
| [backend/requirements.txt](/backend/requirements.txt) | pip requirements | 11 | 0 | 1 | 12 |
| [backend/task\_routes.py](/backend/task_routes.py) | Python | 122 | 32 | 34 | 188 |
| [category\_implementation\_notes.md](/category_implementation_notes.md) | Markdown | 38 | 0 | 12 | 50 |
| [check\_elite\_messaging\_users.py](/check_elite_messaging_users.py) | Python | 9 | 5 | 6 | 20 |
| [check\_pfe\_project\_users.py](/check_pfe_project_users.py) | Python | 9 | 5 | 6 | 20 |
| [create\_admin\_user.py](/create_admin_user.py) | Python | 23 | 6 | 7 | 36 |
| [package-lock.json](/package-lock.json) | JSON | 17,877 | 0 | 1 | 17,878 |
| [package.json](/package.json) | JSON | 48 | 0 | 1 | 49 |
| [public/google-calendar-logo.svg](/public/google-calendar-logo.svg) | XML | 0 | 0 | 1 | 1 |
| [public/google-drive-logo.svg](/public/google-drive-logo.svg) | XML | 0 | 0 | 1 | 1 |
| [public/google-logo.svg](/public/google-logo.svg) | XML | 0 | 0 | 1 | 1 |
| [public/index.html](/public/index.html) | HTML | 25 | 0 | 1 | 26 |
| [public/manifest.json](/public/manifest.json) | JSON | 25 | 0 | 1 | 26 |
| [public/notion-logo.svg](/public/notion-logo.svg) | XML | 0 | 0 | 1 | 1 |
| [src/App.css](/src/App.css) | CSS | 0 | 0 | 1 | 1 |
| [src/App.js](/src/App.js) | JavaScript | 7 | 0 | 3 | 10 |
| [src/components/AdminMaster.js](/src/components/AdminMaster.js) | JavaScript | 1,046 | 25 | 76 | 1,147 |
| [src/components/AdminPanel.js](/src/components/AdminPanel.js) | JavaScript | 1,095 | 31 | 76 | 1,202 |
| [src/components/AuthContainer.js](/src/components/AuthContainer.js) | JavaScript | 92 | 8 | 11 | 111 |
| [src/components/CategoryDropdown.js](/src/components/CategoryDropdown.js) | JavaScript | 84 | 2 | 13 | 99 |
| [src/components/CategoryManager.js](/src/components/CategoryManager.js) | JavaScript | 374 | 17 | 34 | 425 |
| [src/components/ContactsPage.js](/src/components/ContactsPage.js) | JavaScript | 379 | 21 | 43 | 443 |
| [src/components/DashboardPage.js](/src/components/DashboardPage.js) | JavaScript | 345 | 6 | 28 | 379 |
| [src/components/GroupChatPage.js](/src/components/GroupChatPage.js) | JavaScript | 2,381 | 205 | 265 | 2,851 |
| [src/components/MainDashboard.js](/src/components/MainDashboard.js) | JavaScript | 87 | 3 | 10 | 100 |
| [src/components/MessagingPage.js](/src/components/MessagingPage.js) | JavaScript | 2,340 | 223 | 267 | 2,830 |
| [src/components/SearchBar.js](/src/components/SearchBar.js) | JavaScript | 57 | 0 | 6 | 63 |
| [src/components/SecurityPage.js](/src/components/SecurityPage.js) | JavaScript | 202 | 14 | 35 | 251 |
| [src/components/SettingsPage.js](/src/components/SettingsPage.js) | JavaScript | 246 | 10 | 23 | 279 |
| [src/components/Sidebar.js](/src/components/Sidebar.js) | JavaScript | 291 | 14 | 32 | 337 |
| [src/components/SignInForm.js](/src/components/SignInForm.js) | JavaScript | 134 | 3 | 20 | 157 |
| [src/components/SignUpForm.js](/src/components/SignUpForm.js) | JavaScript | 291 | 10 | 35 | 336 |
| [src/components/TaskManagement.js](/src/components/TaskManagement.js) | JavaScript | 821 | 46 | 66 | 933 |
| [src/contexts/AuthContext.js](/src/contexts/AuthContext.js) | JavaScript | 122 | 25 | 34 | 181 |
| [src/index.css](/src/index.css) | CSS | 12 | 0 | 2 | 14 |
| [src/index.js](/src/index.js) | JavaScript | 13 | 0 | 2 | 15 |
| [src/logo.svg](/src/logo.svg) | XML | 1 | 0 | 0 | 1 |
| [src/reportWebVitals.js](/src/reportWebVitals.js) | JavaScript | 12 | 0 | 2 | 14 |
| [src/setupProxy.js](/src/setupProxy.js) | JavaScript | 19 | 0 | 3 | 22 |
| [src/setupTests.js](/src/setupTests.js) | JavaScript | 1 | 4 | 1 | 6 |
| [src/styles/admin.css](/src/styles/admin.css) | CSS | 1,736 | 32 | 325 | 2,093 |
| [src/styles/adminmaster.css](/src/styles/adminmaster.css) | CSS | 1,537 | 19 | 299 | 1,855 |
| [src/styles/auth.css](/src/styles/auth.css) | CSS | 81 | 0 | 9 | 90 |
| [src/styles/category-manager.css](/src/styles/category-manager.css) | CSS | 1,159 | 31 | 203 | 1,393 |
| [src/styles/contacts.css](/src/styles/contacts.css) | CSS | 787 | 9 | 131 | 927 |
| [src/styles/dashboard.css](/src/styles/dashboard.css) | CSS | 320 | 13 | 66 | 399 |
| [src/styles/emoji-picker.css](/src/styles/emoji-picker.css) | CSS | 91 | 8 | 17 | 116 |
| [src/styles/forms.css](/src/styles/forms.css) | CSS | 210 | 2 | 34 | 246 |
| [src/styles/global.css](/src/styles/global.css) | CSS | 88 | 2 | 19 | 109 |
| [src/styles/groupchat.css](/src/styles/groupchat.css) | CSS | 3,214 | 43 | 544 | 3,801 |
| [src/styles/index.css](/src/styles/index.css) | CSS | 366 | 10 | 84 | 460 |
| [src/styles/messages.css](/src/styles/messages.css) | CSS | 282 | 4 | 46 | 332 |
| [src/styles/messaging.css](/src/styles/messaging.css) | CSS | 2,550 | 48 | 439 | 3,037 |
| [src/styles/panels.css](/src/styles/panels.css) | CSS | 72 | 1 | 15 | 88 |
| [src/styles/search-results-list.css](/src/styles/search-results-list.css) | CSS | 80 | 1 | 17 | 98 |
| [src/styles/search.css](/src/styles/search.css) | CSS | 104 | 3 | 19 | 126 |
| [src/styles/security.css](/src/styles/security.css) | CSS | 206 | 5 | 44 | 255 |
| [src/styles/settings.css](/src/styles/settings.css) | CSS | 640 | 18 | 126 | 784 |
| [src/styles/sidebar.css](/src/styles/sidebar.css) | CSS | 1,004 | 13 | 127 | 1,144 |
| [src/styles/tasks.css](/src/styles/tasks.css) | CSS | 867 | 8 | 157 | 1,032 |
| [src/utils/axiosConfig.js](/src/utils/axiosConfig.js) | JavaScript | 24 | 5 | 7 | 36 |
| [src/utils/titleManager.js](/src/utils/titleManager.js) | JavaScript | 35 | 22 | 10 | 67 |
| [test\_admin\_api.py](/test_admin_api.py) | Python | 34 | 6 | 8 | 48 |
| [test\_admin\_users\_api.py](/test_admin_users_api.py) | Python | 34 | 6 | 8 | 48 |
| [test\_signup.py](/test_signup.py) | Python | 15 | 5 | 6 | 26 |
| [test\_signup\_admin\_panel.py](/test_signup_admin_panel.py) | Python | 16 | 5 | 6 | 27 |

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)