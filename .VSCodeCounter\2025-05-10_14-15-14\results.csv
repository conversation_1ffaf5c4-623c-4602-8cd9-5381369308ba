"filename", "language", "Python", "CSS", "JavaScript", "XML", "JSON", "Markdown", "pip requirements", "log", "HTML", "Properties", "comment", "blank", "total"
"c:\PFE_PROJECT\ReactJs\elite\Messages.js", "JavaScript", 0, 0, 78, 0, 0, 0, 0, 0, 0, 0, 0, 11, 89
"c:\PFE_PROJECT\ReactJs\elite\README.md", "Markdown", 0, 0, 0, 0, 0, 38, 0, 0, 0, 0, 0, 33, 71
"c:\PFE_PROJECT\ReactJs\elite\backend\.env", "Properties", 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 0, 1, 4
"c:\PFE_PROJECT\ReactJs\elite\backend\README.md", "Markdown", 0, 0, 0, 0, 0, 62, 0, 0, 0, 0, 0, 23, 85
"c:\PFE_PROJECT\ReactJs\elite\backend\ai_quick_replies.log", "log", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1
"c:\PFE_PROJECT\ReactJs\elite\backend\app.py", "Python", 1702, 0, 0, 0, 0, 0, 0, 0, 0, 0, 267, 445, 2414
"c:\PFE_PROJECT\ReactJs\elite\backend\category_routes.py", "Python", 296, 0, 0, 0, 0, 0, 0, 0, 0, 0, 40, 81, 417
"c:\PFE_PROJECT\ReactJs\elite\backend\file_upload.py", "Python", 152, 0, 0, 0, 0, 0, 0, 0, 0, 0, 35, 47, 234
"c:\PFE_PROJECT\ReactJs\elite\backend\file_utils.py", "Python", 80, 0, 0, 0, 0, 0, 0, 0, 0, 0, 15, 23, 118
"c:\PFE_PROJECT\ReactJs\elite\backend\models.py", "Python", 33, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 36
"c:\PFE_PROJECT\ReactJs\elite\backend\quick_reply_model.log", "log", 0, 0, 0, 0, 0, 0, 0, 485, 0, 0, 0, 31, 516
"c:\PFE_PROJECT\ReactJs\elite\backend\requirements.txt", "pip requirements", 0, 0, 0, 0, 0, 0, 11, 0, 0, 0, 0, 1, 12
"c:\PFE_PROJECT\ReactJs\elite\backend\task_routes.py", "Python", 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 32, 34, 188
"c:\PFE_PROJECT\ReactJs\elite\category_implementation_notes.md", "Markdown", 0, 0, 0, 0, 0, 38, 0, 0, 0, 0, 0, 12, 50
"c:\PFE_PROJECT\ReactJs\elite\check_elite_messaging_users.py", "Python", 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 6, 20
"c:\PFE_PROJECT\ReactJs\elite\check_pfe_project_users.py", "Python", 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 6, 20
"c:\PFE_PROJECT\ReactJs\elite\create_admin_user.py", "Python", 23, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 7, 36
"c:\PFE_PROJECT\ReactJs\elite\package-lock.json", "JSON", 0, 0, 0, 0, 17877, 0, 0, 0, 0, 0, 0, 1, 17878
"c:\PFE_PROJECT\ReactJs\elite\package.json", "JSON", 0, 0, 0, 0, 48, 0, 0, 0, 0, 0, 0, 1, 49
"c:\PFE_PROJECT\ReactJs\elite\public\google-calendar-logo.svg", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1
"c:\PFE_PROJECT\ReactJs\elite\public\google-drive-logo.svg", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1
"c:\PFE_PROJECT\ReactJs\elite\public\google-logo.svg", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1
"c:\PFE_PROJECT\ReactJs\elite\public\index.html", "HTML", 0, 0, 0, 0, 0, 0, 0, 0, 25, 0, 0, 1, 26
"c:\PFE_PROJECT\ReactJs\elite\public\manifest.json", "JSON", 0, 0, 0, 0, 25, 0, 0, 0, 0, 0, 0, 1, 26
"c:\PFE_PROJECT\ReactJs\elite\public\notion-logo.svg", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1
"c:\PFE_PROJECT\ReactJs\elite\src\App.css", "CSS", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1
"c:\PFE_PROJECT\ReactJs\elite\src\App.js", "JavaScript", 0, 0, 7, 0, 0, 0, 0, 0, 0, 0, 0, 3, 10
"c:\PFE_PROJECT\ReactJs\elite\src\components\AdminMaster.js", "JavaScript", 0, 0, 1046, 0, 0, 0, 0, 0, 0, 0, 25, 76, 1147
"c:\PFE_PROJECT\ReactJs\elite\src\components\AdminPanel.js", "JavaScript", 0, 0, 1095, 0, 0, 0, 0, 0, 0, 0, 31, 76, 1202
"c:\PFE_PROJECT\ReactJs\elite\src\components\AuthContainer.js", "JavaScript", 0, 0, 92, 0, 0, 0, 0, 0, 0, 0, 8, 11, 111
"c:\PFE_PROJECT\ReactJs\elite\src\components\CategoryDropdown.js", "JavaScript", 0, 0, 84, 0, 0, 0, 0, 0, 0, 0, 2, 13, 99
"c:\PFE_PROJECT\ReactJs\elite\src\components\CategoryManager.js", "JavaScript", 0, 0, 374, 0, 0, 0, 0, 0, 0, 0, 17, 34, 425
"c:\PFE_PROJECT\ReactJs\elite\src\components\ContactsPage.js", "JavaScript", 0, 0, 379, 0, 0, 0, 0, 0, 0, 0, 21, 43, 443
"c:\PFE_PROJECT\ReactJs\elite\src\components\DashboardPage.js", "JavaScript", 0, 0, 345, 0, 0, 0, 0, 0, 0, 0, 6, 28, 379
"c:\PFE_PROJECT\ReactJs\elite\src\components\GroupChatPage.js", "JavaScript", 0, 0, 2381, 0, 0, 0, 0, 0, 0, 0, 205, 265, 2851
"c:\PFE_PROJECT\ReactJs\elite\src\components\MainDashboard.js", "JavaScript", 0, 0, 87, 0, 0, 0, 0, 0, 0, 0, 3, 10, 100
"c:\PFE_PROJECT\ReactJs\elite\src\components\MessagingPage.js", "JavaScript", 0, 0, 2340, 0, 0, 0, 0, 0, 0, 0, 223, 267, 2830
"c:\PFE_PROJECT\ReactJs\elite\src\components\SearchBar.js", "JavaScript", 0, 0, 57, 0, 0, 0, 0, 0, 0, 0, 0, 6, 63
"c:\PFE_PROJECT\ReactJs\elite\src\components\SecurityPage.js", "JavaScript", 0, 0, 202, 0, 0, 0, 0, 0, 0, 0, 14, 35, 251
"c:\PFE_PROJECT\ReactJs\elite\src\components\SettingsPage.js", "JavaScript", 0, 0, 246, 0, 0, 0, 0, 0, 0, 0, 10, 23, 279
"c:\PFE_PROJECT\ReactJs\elite\src\components\Sidebar.js", "JavaScript", 0, 0, 291, 0, 0, 0, 0, 0, 0, 0, 14, 32, 337
"c:\PFE_PROJECT\ReactJs\elite\src\components\SignInForm.js", "JavaScript", 0, 0, 134, 0, 0, 0, 0, 0, 0, 0, 3, 20, 157
"c:\PFE_PROJECT\ReactJs\elite\src\components\SignUpForm.js", "JavaScript", 0, 0, 291, 0, 0, 0, 0, 0, 0, 0, 10, 35, 336
"c:\PFE_PROJECT\ReactJs\elite\src\components\TaskManagement.js", "JavaScript", 0, 0, 821, 0, 0, 0, 0, 0, 0, 0, 46, 66, 933
"c:\PFE_PROJECT\ReactJs\elite\src\contexts\AuthContext.js", "JavaScript", 0, 0, 122, 0, 0, 0, 0, 0, 0, 0, 25, 34, 181
"c:\PFE_PROJECT\ReactJs\elite\src\index.css", "CSS", 0, 12, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 14
"c:\PFE_PROJECT\ReactJs\elite\src\index.js", "JavaScript", 0, 0, 13, 0, 0, 0, 0, 0, 0, 0, 0, 2, 15
"c:\PFE_PROJECT\ReactJs\elite\src\logo.svg", "XML", 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\PFE_PROJECT\ReactJs\elite\src\reportWebVitals.js", "JavaScript", 0, 0, 12, 0, 0, 0, 0, 0, 0, 0, 0, 2, 14
"c:\PFE_PROJECT\ReactJs\elite\src\setupProxy.js", "JavaScript", 0, 0, 19, 0, 0, 0, 0, 0, 0, 0, 0, 3, 22
"c:\PFE_PROJECT\ReactJs\elite\src\setupTests.js", "JavaScript", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 4, 1, 6
"c:\PFE_PROJECT\ReactJs\elite\src\styles\admin.css", "CSS", 0, 1736, 0, 0, 0, 0, 0, 0, 0, 0, 32, 325, 2093
"c:\PFE_PROJECT\ReactJs\elite\src\styles\adminmaster.css", "CSS", 0, 1537, 0, 0, 0, 0, 0, 0, 0, 0, 19, 299, 1855
"c:\PFE_PROJECT\ReactJs\elite\src\styles\auth.css", "CSS", 0, 81, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 90
"c:\PFE_PROJECT\ReactJs\elite\src\styles\category-manager.css", "CSS", 0, 1159, 0, 0, 0, 0, 0, 0, 0, 0, 31, 203, 1393
"c:\PFE_PROJECT\ReactJs\elite\src\styles\contacts.css", "CSS", 0, 787, 0, 0, 0, 0, 0, 0, 0, 0, 9, 131, 927
"c:\PFE_PROJECT\ReactJs\elite\src\styles\dashboard.css", "CSS", 0, 320, 0, 0, 0, 0, 0, 0, 0, 0, 13, 66, 399
"c:\PFE_PROJECT\ReactJs\elite\src\styles\emoji-picker.css", "CSS", 0, 91, 0, 0, 0, 0, 0, 0, 0, 0, 8, 17, 116
"c:\PFE_PROJECT\ReactJs\elite\src\styles\forms.css", "CSS", 0, 210, 0, 0, 0, 0, 0, 0, 0, 0, 2, 34, 246
"c:\PFE_PROJECT\ReactJs\elite\src\styles\global.css", "CSS", 0, 88, 0, 0, 0, 0, 0, 0, 0, 0, 2, 19, 109
"c:\PFE_PROJECT\ReactJs\elite\src\styles\groupchat.css", "CSS", 0, 3214, 0, 0, 0, 0, 0, 0, 0, 0, 43, 544, 3801
"c:\PFE_PROJECT\ReactJs\elite\src\styles\index.css", "CSS", 0, 366, 0, 0, 0, 0, 0, 0, 0, 0, 10, 84, 460
"c:\PFE_PROJECT\ReactJs\elite\src\styles\messages.css", "CSS", 0, 282, 0, 0, 0, 0, 0, 0, 0, 0, 4, 46, 332
"c:\PFE_PROJECT\ReactJs\elite\src\styles\messaging.css", "CSS", 0, 2550, 0, 0, 0, 0, 0, 0, 0, 0, 48, 439, 3037
"c:\PFE_PROJECT\ReactJs\elite\src\styles\panels.css", "CSS", 0, 72, 0, 0, 0, 0, 0, 0, 0, 0, 1, 15, 88
"c:\PFE_PROJECT\ReactJs\elite\src\styles\search-results-list.css", "CSS", 0, 80, 0, 0, 0, 0, 0, 0, 0, 0, 1, 17, 98
"c:\PFE_PROJECT\ReactJs\elite\src\styles\search.css", "CSS", 0, 104, 0, 0, 0, 0, 0, 0, 0, 0, 3, 19, 126
"c:\PFE_PROJECT\ReactJs\elite\src\styles\security.css", "CSS", 0, 206, 0, 0, 0, 0, 0, 0, 0, 0, 5, 44, 255
"c:\PFE_PROJECT\ReactJs\elite\src\styles\settings.css", "CSS", 0, 640, 0, 0, 0, 0, 0, 0, 0, 0, 18, 126, 784
"c:\PFE_PROJECT\ReactJs\elite\src\styles\sidebar.css", "CSS", 0, 1004, 0, 0, 0, 0, 0, 0, 0, 0, 13, 127, 1144
"c:\PFE_PROJECT\ReactJs\elite\src\styles\tasks.css", "CSS", 0, 867, 0, 0, 0, 0, 0, 0, 0, 0, 8, 157, 1032
"c:\PFE_PROJECT\ReactJs\elite\src\utils\axiosConfig.js", "JavaScript", 0, 0, 24, 0, 0, 0, 0, 0, 0, 0, 5, 7, 36
"c:\PFE_PROJECT\ReactJs\elite\src\utils\titleManager.js", "JavaScript", 0, 0, 35, 0, 0, 0, 0, 0, 0, 0, 22, 10, 67
"c:\PFE_PROJECT\ReactJs\elite\test_admin_api.py", "Python", 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 8, 48
"c:\PFE_PROJECT\ReactJs\elite\test_admin_users_api.py", "Python", 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 8, 48
"c:\PFE_PROJECT\ReactJs\elite\test_signup.py", "Python", 15, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 6, 26
"c:\PFE_PROJECT\ReactJs\elite\test_signup_admin_panel.py", "Python", 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 6, 27
"Total", "-", 2525, 15406, 10576, 1, 17950, 138, 11, 485, 25, 3, 1391, 4627, 53138