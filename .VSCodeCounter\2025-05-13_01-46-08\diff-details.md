# Diff Details

Date : 2025-05-13 01:46:08

Directory c:\\PFE_PROJECT\\ReactJs\\elite

Total : 20 files,  1837 codes, 325 comments, 338 blanks, all 2500 lines

[Summary](results.md) / [Details](details.md) / [Diff Summary](diff.md) / Diff Details

## Files
| filename | language | code | comment | blank | total |
| :--- | :--- | ---: | ---: | ---: | ---: |
| [backend/app.py](/backend/app.py) | Python | 117 | 33 | 29 | 179 |
| [backend/encryption.py](/backend/encryption.py) | Python | 88 | 17 | 24 | 129 |
| [backend/requirements.txt](/backend/requirements.txt) | pip requirements | 1 | 0 | 0 | 1 |
| [package-lock.json](/package-lock.json) | JSON | 7 | 0 | 0 | 7 |
| [package.json](/package.json) | JSON | 1 | 0 | 0 | 1 |
| [src/components/AdminMaster.js](/src/components/AdminMaster.js) | JavaScript | 44 | 8 | 2 | 54 |
| [src/components/AdminPanel.js](/src/components/AdminPanel.js) | JavaScript | 32 | 0 | 0 | 32 |
| [src/components/AuthContainer.js](/src/components/AuthContainer.js) | JavaScript | 3 | 0 | 0 | 3 |
| [src/components/GroupChatPage.js](/src/components/GroupChatPage.js) | JavaScript | 254 | 56 | 53 | 363 |
| [src/components/MessagingPage.js](/src/components/MessagingPage.js) | JavaScript | 263 | 40 | 44 | 347 |
| [src/components/SettingsPage.js](/src/components/SettingsPage.js) | JavaScript | 30 | 0 | 1 | 31 |
| [src/components/UrgencySelector.js](/src/components/UrgencySelector.js) | JavaScript | 115 | 15 | 12 | 142 |
| [src/contexts/EncryptionContext.js](/src/contexts/EncryptionContext.js) | JavaScript | 139 | 16 | 32 | 187 |
| [src/styles/admin.css](/src/styles/admin.css) | CSS | 16 | 0 | 4 | 20 |
| [src/styles/groupchat.css](/src/styles/groupchat.css) | CSS | 219 | 4 | 23 | 246 |
| [src/styles/messaging.css](/src/styles/messaging.css) | CSS | 136 | 8 | 28 | 172 |
| [src/styles/settings.css](/src/styles/settings.css) | CSS | 9 | 0 | 2 | 11 |
| [src/styles/urgency-selector.css](/src/styles/urgency-selector.css) | CSS | 163 | 3 | 32 | 198 |
| [src/utils/encryption.js](/src/utils/encryption.js) | JavaScript | 77 | 60 | 16 | 153 |
| [src/utils/globalnotificationmanager.js](/src/utils/globalnotificationmanager.js) | JavaScript | 123 | 65 | 36 | 224 |

[Summary](results.md) / [Details](details.md) / [Diff Summary](diff.md) / Diff Details