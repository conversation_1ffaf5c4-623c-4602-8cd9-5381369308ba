# Diff Summary

Date : 2025-05-13 01:46:08

Directory c:\\PFE_PROJECT\\ReactJs\\elite

Total : 20 files,  1837 codes, 325 comments, 338 blanks, all 2500 lines

[Summary](results.md) / [Details](details.md) / Diff Summary / [Diff Details](diff-details.md)

## Languages
| language | files | code | comment | blank | total |
| :--- | ---: | ---: | ---: | ---: | ---: |
| JavaScript | 10 | 1,080 | 260 | 196 | 1,536 |
| CSS | 5 | 543 | 15 | 89 | 647 |
| Python | 2 | 205 | 50 | 53 | 308 |
| JSON | 2 | 8 | 0 | 0 | 8 |
| pip requirements | 1 | 1 | 0 | 0 | 1 |

## Directories
| path | files | code | comment | blank | total |
| :--- | ---: | ---: | ---: | ---: | ---: |
| . | 20 | 1,837 | 325 | 338 | 2,500 |
| . (Files) | 2 | 8 | 0 | 0 | 8 |
| backend | 3 | 206 | 50 | 53 | 309 |
| src | 15 | 1,623 | 275 | 285 | 2,183 |
| src\\components | 7 | 741 | 119 | 112 | 972 |
| src\\contexts | 1 | 139 | 16 | 32 | 187 |
| src\\styles | 5 | 543 | 15 | 89 | 647 |
| src\\utils | 2 | 200 | 125 | 52 | 377 |

[Summary](results.md) / [Details](details.md) / Diff Summary / [Diff Details](diff-details.md)