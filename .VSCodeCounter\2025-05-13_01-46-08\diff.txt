Date : 2025-05-13 01:46:08
Directory : c:\PFE_PROJECT\ReactJs\elite
Total : 20 files,  1837 codes, 325 comments, 338 blanks, all 2500 lines

Languages
+------------------+------------+------------+------------+------------+------------+
| language         | files      | code       | comment    | blank      | total      |
+------------------+------------+------------+------------+------------+------------+
| JavaScript       |         10 |      1,080 |        260 |        196 |      1,536 |
| CSS              |          5 |        543 |         15 |         89 |        647 |
| Python           |          2 |        205 |         50 |         53 |        308 |
| JSON             |          2 |          8 |          0 |          0 |          8 |
| pip requirements |          1 |          1 |          0 |          0 |          1 |
+------------------+------------+------------+------------+------------+------------+

Directories
+---------------------------------------------------------------------+------------+------------+------------+------------+------------+
| path                                                                | files      | code       | comment    | blank      | total      |
+---------------------------------------------------------------------+------------+------------+------------+------------+------------+
| .                                                                   |         20 |      1,837 |        325 |        338 |      2,500 |
| . (Files)                                                           |          2 |          8 |          0 |          0 |          8 |
| backend                                                             |          3 |        206 |         50 |         53 |        309 |
| src                                                                 |         15 |      1,623 |        275 |        285 |      2,183 |
| src\components                                                      |          7 |        741 |        119 |        112 |        972 |
| src\contexts                                                        |          1 |        139 |         16 |         32 |        187 |
| src\styles                                                          |          5 |        543 |         15 |         89 |        647 |
| src\utils                                                           |          2 |        200 |        125 |         52 |        377 |
+---------------------------------------------------------------------+------------+------------+------------+------------+------------+

Files
+---------------------------------------------------------------------+------------------+------------+------------+------------+------------+
| filename                                                            | language         | code       | comment    | blank      | total      |
+---------------------------------------------------------------------+------------------+------------+------------+------------+------------+
| c:\PFE_PROJECT\ReactJs\elite\backend\app.py                         | Python           |        117 |         33 |         29 |        179 |
| c:\PFE_PROJECT\ReactJs\elite\backend\encryption.py                  | Python           |         88 |         17 |         24 |        129 |
| c:\PFE_PROJECT\ReactJs\elite\backend\requirements.txt               | pip requirements |          1 |          0 |          0 |          1 |
| c:\PFE_PROJECT\ReactJs\elite\package-lock.json                      | JSON             |          7 |          0 |          0 |          7 |
| c:\PFE_PROJECT\ReactJs\elite\package.json                           | JSON             |          1 |          0 |          0 |          1 |
| c:\PFE_PROJECT\ReactJs\elite\src\components\AdminMaster.js          | JavaScript       |         44 |          8 |          2 |         54 |
| c:\PFE_PROJECT\ReactJs\elite\src\components\AdminPanel.js           | JavaScript       |         32 |          0 |          0 |         32 |
| c:\PFE_PROJECT\ReactJs\elite\src\components\AuthContainer.js        | JavaScript       |          3 |          0 |          0 |          3 |
| c:\PFE_PROJECT\ReactJs\elite\src\components\GroupChatPage.js        | JavaScript       |        254 |         56 |         53 |        363 |
| c:\PFE_PROJECT\ReactJs\elite\src\components\MessagingPage.js        | JavaScript       |        263 |         40 |         44 |        347 |
| c:\PFE_PROJECT\ReactJs\elite\src\components\SettingsPage.js         | JavaScript       |         30 |          0 |          1 |         31 |
| c:\PFE_PROJECT\ReactJs\elite\src\components\UrgencySelector.js      | JavaScript       |        115 |         15 |         12 |        142 |
| c:\PFE_PROJECT\ReactJs\elite\src\contexts\EncryptionContext.js      | JavaScript       |        139 |         16 |         32 |        187 |
| c:\PFE_PROJECT\ReactJs\elite\src\styles\admin.css                   | CSS              |         16 |          0 |          4 |         20 |
| c:\PFE_PROJECT\ReactJs\elite\src\styles\groupchat.css               | CSS              |        219 |          4 |         23 |        246 |
| c:\PFE_PROJECT\ReactJs\elite\src\styles\messaging.css               | CSS              |        136 |          8 |         28 |        172 |
| c:\PFE_PROJECT\ReactJs\elite\src\styles\settings.css                | CSS              |          9 |          0 |          2 |         11 |
| c:\PFE_PROJECT\ReactJs\elite\src\styles\urgency-selector.css        | CSS              |        163 |          3 |         32 |        198 |
| c:\PFE_PROJECT\ReactJs\elite\src\utils\encryption.js                | JavaScript       |         77 |         60 |         16 |        153 |
| c:\PFE_PROJECT\ReactJs\elite\src\utils\globalnotificationmanager.js | JavaScript       |        123 |         65 |         36 |        224 |
| Total                                                               |                  |      1,837 |        325 |        338 |      2,500 |
+---------------------------------------------------------------------+------------------+------------+------------+------------+------------+