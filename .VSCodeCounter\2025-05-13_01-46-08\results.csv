"filename", "language", "Python", "JavaScript", "Markdown", "XML", "JSON", "HTML", "CSS", "pip requirements", "log", "Properties", "comment", "blank", "total"
"c:\PFE_PROJECT\ReactJs\elite\Messages.js", "JavaScript", 0, 78, 0, 0, 0, 0, 0, 0, 0, 0, 0, 11, 89
"c:\PFE_PROJECT\ReactJs\elite\README.md", "Markdown", 0, 0, 38, 0, 0, 0, 0, 0, 0, 0, 0, 33, 71
"c:\PFE_PROJECT\ReactJs\elite\backend\.env", "Properties", 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 0, 1, 4
"c:\PFE_PROJECT\ReactJs\elite\backend\README.md", "Markdown", 0, 0, 62, 0, 0, 0, 0, 0, 0, 0, 0, 23, 85
"c:\PFE_PROJECT\ReactJs\elite\backend\ai_quick_replies.log", "log", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1
"c:\PFE_PROJECT\ReactJs\elite\backend\app.py", "Python", 1819, 0, 0, 0, 0, 0, 0, 0, 0, 0, 300, 474, 2593
"c:\PFE_PROJECT\ReactJs\elite\backend\category_routes.py", "Python", 296, 0, 0, 0, 0, 0, 0, 0, 0, 0, 40, 81, 417
"c:\PFE_PROJECT\ReactJs\elite\backend\encryption.py", "Python", 88, 0, 0, 0, 0, 0, 0, 0, 0, 0, 17, 24, 129
"c:\PFE_PROJECT\ReactJs\elite\backend\file_upload.py", "Python", 152, 0, 0, 0, 0, 0, 0, 0, 0, 0, 35, 47, 234
"c:\PFE_PROJECT\ReactJs\elite\backend\file_utils.py", "Python", 80, 0, 0, 0, 0, 0, 0, 0, 0, 0, 15, 23, 118
"c:\PFE_PROJECT\ReactJs\elite\backend\models.py", "Python", 33, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 36
"c:\PFE_PROJECT\ReactJs\elite\backend\quick_reply_model.log", "log", 0, 0, 0, 0, 0, 0, 0, 0, 485, 0, 0, 31, 516
"c:\PFE_PROJECT\ReactJs\elite\backend\requirements.txt", "pip requirements", 0, 0, 0, 0, 0, 0, 0, 12, 0, 0, 0, 1, 13
"c:\PFE_PROJECT\ReactJs\elite\backend\task_routes.py", "Python", 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 32, 34, 188
"c:\PFE_PROJECT\ReactJs\elite\category_implementation_notes.md", "Markdown", 0, 0, 38, 0, 0, 0, 0, 0, 0, 0, 0, 12, 50
"c:\PFE_PROJECT\ReactJs\elite\check_elite_messaging_users.py", "Python", 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 6, 20
"c:\PFE_PROJECT\ReactJs\elite\check_pfe_project_users.py", "Python", 9, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 6, 20
"c:\PFE_PROJECT\ReactJs\elite\create_admin_user.py", "Python", 23, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 7, 36
"c:\PFE_PROJECT\ReactJs\elite\package-lock.json", "JSON", 0, 0, 0, 0, 17884, 0, 0, 0, 0, 0, 0, 1, 17885
"c:\PFE_PROJECT\ReactJs\elite\package.json", "JSON", 0, 0, 0, 0, 49, 0, 0, 0, 0, 0, 0, 1, 50
"c:\PFE_PROJECT\ReactJs\elite\public\google-calendar-logo.svg", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1
"c:\PFE_PROJECT\ReactJs\elite\public\google-drive-logo.svg", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1
"c:\PFE_PROJECT\ReactJs\elite\public\google-logo.svg", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1
"c:\PFE_PROJECT\ReactJs\elite\public\index.html", "HTML", 0, 0, 0, 0, 0, 25, 0, 0, 0, 0, 0, 1, 26
"c:\PFE_PROJECT\ReactJs\elite\public\manifest.json", "JSON", 0, 0, 0, 0, 25, 0, 0, 0, 0, 0, 0, 1, 26
"c:\PFE_PROJECT\ReactJs\elite\public\notion-logo.svg", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1
"c:\PFE_PROJECT\ReactJs\elite\src\App.css", "CSS", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1
"c:\PFE_PROJECT\ReactJs\elite\src\App.js", "JavaScript", 0, 7, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 10
"c:\PFE_PROJECT\ReactJs\elite\src\components\AdminMaster.js", "JavaScript", 0, 1090, 0, 0, 0, 0, 0, 0, 0, 0, 33, 78, 1201
"c:\PFE_PROJECT\ReactJs\elite\src\components\AdminPanel.js", "JavaScript", 0, 1127, 0, 0, 0, 0, 0, 0, 0, 0, 31, 76, 1234
"c:\PFE_PROJECT\ReactJs\elite\src\components\AuthContainer.js", "JavaScript", 0, 95, 0, 0, 0, 0, 0, 0, 0, 0, 8, 11, 114
"c:\PFE_PROJECT\ReactJs\elite\src\components\CategoryDropdown.js", "JavaScript", 0, 84, 0, 0, 0, 0, 0, 0, 0, 0, 2, 13, 99
"c:\PFE_PROJECT\ReactJs\elite\src\components\CategoryManager.js", "JavaScript", 0, 374, 0, 0, 0, 0, 0, 0, 0, 0, 17, 34, 425
"c:\PFE_PROJECT\ReactJs\elite\src\components\ContactsPage.js", "JavaScript", 0, 379, 0, 0, 0, 0, 0, 0, 0, 0, 21, 43, 443
"c:\PFE_PROJECT\ReactJs\elite\src\components\DashboardPage.js", "JavaScript", 0, 345, 0, 0, 0, 0, 0, 0, 0, 0, 6, 28, 379
"c:\PFE_PROJECT\ReactJs\elite\src\components\GroupChatPage.js", "JavaScript", 0, 2635, 0, 0, 0, 0, 0, 0, 0, 0, 261, 318, 3214
"c:\PFE_PROJECT\ReactJs\elite\src\components\MainDashboard.js", "JavaScript", 0, 87, 0, 0, 0, 0, 0, 0, 0, 0, 3, 10, 100
"c:\PFE_PROJECT\ReactJs\elite\src\components\MessagingPage.js", "JavaScript", 0, 2603, 0, 0, 0, 0, 0, 0, 0, 0, 263, 311, 3177
"c:\PFE_PROJECT\ReactJs\elite\src\components\SearchBar.js", "JavaScript", 0, 57, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 63
"c:\PFE_PROJECT\ReactJs\elite\src\components\SecurityPage.js", "JavaScript", 0, 202, 0, 0, 0, 0, 0, 0, 0, 0, 14, 35, 251
"c:\PFE_PROJECT\ReactJs\elite\src\components\SettingsPage.js", "JavaScript", 0, 276, 0, 0, 0, 0, 0, 0, 0, 0, 10, 24, 310
"c:\PFE_PROJECT\ReactJs\elite\src\components\Sidebar.js", "JavaScript", 0, 291, 0, 0, 0, 0, 0, 0, 0, 0, 14, 32, 337
"c:\PFE_PROJECT\ReactJs\elite\src\components\SignInForm.js", "JavaScript", 0, 134, 0, 0, 0, 0, 0, 0, 0, 0, 3, 20, 157
"c:\PFE_PROJECT\ReactJs\elite\src\components\SignUpForm.js", "JavaScript", 0, 291, 0, 0, 0, 0, 0, 0, 0, 0, 10, 35, 336
"c:\PFE_PROJECT\ReactJs\elite\src\components\TaskManagement.js", "JavaScript", 0, 821, 0, 0, 0, 0, 0, 0, 0, 0, 46, 66, 933
"c:\PFE_PROJECT\ReactJs\elite\src\components\UrgencySelector.js", "JavaScript", 0, 115, 0, 0, 0, 0, 0, 0, 0, 0, 15, 12, 142
"c:\PFE_PROJECT\ReactJs\elite\src\contexts\AuthContext.js", "JavaScript", 0, 122, 0, 0, 0, 0, 0, 0, 0, 0, 25, 34, 181
"c:\PFE_PROJECT\ReactJs\elite\src\contexts\EncryptionContext.js", "JavaScript", 0, 139, 0, 0, 0, 0, 0, 0, 0, 0, 16, 32, 187
"c:\PFE_PROJECT\ReactJs\elite\src\index.css", "CSS", 0, 0, 0, 0, 0, 0, 12, 0, 0, 0, 0, 2, 14
"c:\PFE_PROJECT\ReactJs\elite\src\index.js", "JavaScript", 0, 13, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 15
"c:\PFE_PROJECT\ReactJs\elite\src\logo.svg", "XML", 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 1
"c:\PFE_PROJECT\ReactJs\elite\src\reportWebVitals.js", "JavaScript", 0, 12, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 14
"c:\PFE_PROJECT\ReactJs\elite\src\setupProxy.js", "JavaScript", 0, 19, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 22
"c:\PFE_PROJECT\ReactJs\elite\src\setupTests.js", "JavaScript", 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 4, 1, 6
"c:\PFE_PROJECT\ReactJs\elite\src\styles\admin.css", "CSS", 0, 0, 0, 0, 0, 0, 1752, 0, 0, 0, 32, 329, 2113
"c:\PFE_PROJECT\ReactJs\elite\src\styles\adminmaster.css", "CSS", 0, 0, 0, 0, 0, 0, 1537, 0, 0, 0, 19, 299, 1855
"c:\PFE_PROJECT\ReactJs\elite\src\styles\auth.css", "CSS", 0, 0, 0, 0, 0, 0, 81, 0, 0, 0, 0, 9, 90
"c:\PFE_PROJECT\ReactJs\elite\src\styles\category-manager.css", "CSS", 0, 0, 0, 0, 0, 0, 1159, 0, 0, 0, 31, 203, 1393
"c:\PFE_PROJECT\ReactJs\elite\src\styles\contacts.css", "CSS", 0, 0, 0, 0, 0, 0, 787, 0, 0, 0, 9, 131, 927
"c:\PFE_PROJECT\ReactJs\elite\src\styles\dashboard.css", "CSS", 0, 0, 0, 0, 0, 0, 320, 0, 0, 0, 13, 66, 399
"c:\PFE_PROJECT\ReactJs\elite\src\styles\emoji-picker.css", "CSS", 0, 0, 0, 0, 0, 0, 91, 0, 0, 0, 8, 17, 116
"c:\PFE_PROJECT\ReactJs\elite\src\styles\forms.css", "CSS", 0, 0, 0, 0, 0, 0, 210, 0, 0, 0, 2, 34, 246
"c:\PFE_PROJECT\ReactJs\elite\src\styles\global.css", "CSS", 0, 0, 0, 0, 0, 0, 88, 0, 0, 0, 2, 19, 109
"c:\PFE_PROJECT\ReactJs\elite\src\styles\groupchat.css", "CSS", 0, 0, 0, 0, 0, 0, 3433, 0, 0, 0, 47, 567, 4047
"c:\PFE_PROJECT\ReactJs\elite\src\styles\index.css", "CSS", 0, 0, 0, 0, 0, 0, 366, 0, 0, 0, 10, 84, 460
"c:\PFE_PROJECT\ReactJs\elite\src\styles\messages.css", "CSS", 0, 0, 0, 0, 0, 0, 282, 0, 0, 0, 4, 46, 332
"c:\PFE_PROJECT\ReactJs\elite\src\styles\messaging.css", "CSS", 0, 0, 0, 0, 0, 0, 2686, 0, 0, 0, 56, 467, 3209
"c:\PFE_PROJECT\ReactJs\elite\src\styles\panels.css", "CSS", 0, 0, 0, 0, 0, 0, 72, 0, 0, 0, 1, 15, 88
"c:\PFE_PROJECT\ReactJs\elite\src\styles\search-results-list.css", "CSS", 0, 0, 0, 0, 0, 0, 80, 0, 0, 0, 1, 17, 98
"c:\PFE_PROJECT\ReactJs\elite\src\styles\search.css", "CSS", 0, 0, 0, 0, 0, 0, 104, 0, 0, 0, 3, 19, 126
"c:\PFE_PROJECT\ReactJs\elite\src\styles\security.css", "CSS", 0, 0, 0, 0, 0, 0, 206, 0, 0, 0, 5, 44, 255
"c:\PFE_PROJECT\ReactJs\elite\src\styles\settings.css", "CSS", 0, 0, 0, 0, 0, 0, 649, 0, 0, 0, 18, 128, 795
"c:\PFE_PROJECT\ReactJs\elite\src\styles\sidebar.css", "CSS", 0, 0, 0, 0, 0, 0, 1004, 0, 0, 0, 13, 127, 1144
"c:\PFE_PROJECT\ReactJs\elite\src\styles\tasks.css", "CSS", 0, 0, 0, 0, 0, 0, 867, 0, 0, 0, 8, 157, 1032
"c:\PFE_PROJECT\ReactJs\elite\src\styles\urgency-selector.css", "CSS", 0, 0, 0, 0, 0, 0, 163, 0, 0, 0, 3, 32, 198
"c:\PFE_PROJECT\ReactJs\elite\src\utils\axiosConfig.js", "JavaScript", 0, 24, 0, 0, 0, 0, 0, 0, 0, 0, 5, 7, 36
"c:\PFE_PROJECT\ReactJs\elite\src\utils\encryption.js", "JavaScript", 0, 77, 0, 0, 0, 0, 0, 0, 0, 0, 60, 16, 153
"c:\PFE_PROJECT\ReactJs\elite\src\utils\globalnotificationmanager.js", "JavaScript", 0, 123, 0, 0, 0, 0, 0, 0, 0, 0, 65, 36, 224
"c:\PFE_PROJECT\ReactJs\elite\src\utils\titleManager.js", "JavaScript", 0, 35, 0, 0, 0, 0, 0, 0, 0, 0, 22, 10, 67
"c:\PFE_PROJECT\ReactJs\elite\test_admin_api.py", "Python", 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 8, 48
"c:\PFE_PROJECT\ReactJs\elite\test_admin_users_api.py", "Python", 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 8, 48
"c:\PFE_PROJECT\ReactJs\elite\test_signup.py", "Python", 15, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 6, 26
"c:\PFE_PROJECT\ReactJs\elite\test_signup_admin_panel.py", "Python", 16, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 6, 27
"Total", "-", 2730, 11656, 138, 1, 17958, 25, 15949, 12, 485, 3, 1716, 4965, 55638