{"file:///c%3A/PFE_PROJECT/ReactJs/elite/check_elite_messaging_users.py": {"language": "Python", "code": 9, "comment": 5, "blank": 6}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/Messages.js": {"language": "JavaScript", "code": 78, "comment": 0, "blank": 11}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/create_admin_user.py": {"language": "Python", "code": 23, "comment": 6, "blank": 7}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/check_pfe_project_users.py": {"language": "Python", "code": 9, "comment": 5, "blank": 6}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/category_implementation_notes.md": {"language": "<PERSON><PERSON>", "code": 38, "comment": 0, "blank": 12}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/test_signup_admin_panel.py": {"language": "Python", "code": 16, "comment": 5, "blank": 6}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/test_signup.py": {"language": "Python", "code": 15, "comment": 5, "blank": 6}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/test_admin_api.py": {"language": "Python", "code": 34, "comment": 6, "blank": 8}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/test_admin_users_api.py": {"language": "Python", "code": 34, "comment": 6, "blank": 8}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/public/google-logo.svg": {"language": "XML", "code": 0, "comment": 0, "blank": 1}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/package.json": {"language": "JSON", "code": 49, "comment": 0, "blank": 1}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/README.md": {"language": "<PERSON><PERSON>", "code": 38, "comment": 0, "blank": 33}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/src/setupProxy.js": {"language": "JavaScript", "code": 19, "comment": 0, "blank": 3}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/public/index.html": {"language": "HTML", "code": 25, "comment": 0, "blank": 1}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/src/reportWebVitals.js": {"language": "JavaScript", "code": 12, "comment": 0, "blank": 2}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/src/logo.svg": {"language": "XML", "code": 1, "comment": 0, "blank": 0}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/src/index.js": {"language": "JavaScript", "code": 13, "comment": 0, "blank": 2}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/public/google-drive-logo.svg": {"language": "XML", "code": 0, "comment": 0, "blank": 1}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/src/index.css": {"language": "CSS", "code": 12, "comment": 0, "blank": 2}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/public/manifest.json": {"language": "JSON", "code": 25, "comment": 0, "blank": 1}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/public/notion-logo.svg": {"language": "XML", "code": 0, "comment": 0, "blank": 1}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/src/utils/encryption.js": {"language": "JavaScript", "code": 77, "comment": 60, "blank": 16}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/public/google-calendar-logo.svg": {"language": "XML", "code": 0, "comment": 0, "blank": 1}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/src/contexts/EncryptionContext.js": {"language": "JavaScript", "code": 139, "comment": 16, "blank": 32}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/src/contexts/AuthContext.js": {"language": "JavaScript", "code": 122, "comment": 25, "blank": 34}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/src/setupTests.js": {"language": "JavaScript", "code": 1, "comment": 4, "blank": 1}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/src/utils/globalnotificationmanager.js": {"language": "JavaScript", "code": 123, "comment": 65, "blank": 36}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/src/utils/titleManager.js": {"language": "JavaScript", "code": 35, "comment": 22, "blank": 10}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/src/utils/axiosConfig.js": {"language": "JavaScript", "code": 24, "comment": 5, "blank": 7}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/src/styles/urgency-selector.css": {"language": "CSS", "code": 163, "comment": 3, "blank": 32}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/src/styles/tasks.css": {"language": "CSS", "code": 867, "comment": 8, "blank": 157}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/src/styles/settings.css": {"language": "CSS", "code": 649, "comment": 18, "blank": 128}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/src/styles/security.css": {"language": "CSS", "code": 206, "comment": 5, "blank": 44}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/src/styles/sidebar.css": {"language": "CSS", "code": 1004, "comment": 13, "blank": 127}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/src/styles/search.css": {"language": "CSS", "code": 104, "comment": 3, "blank": 19}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/src/styles/search-results-list.css": {"language": "CSS", "code": 80, "comment": 1, "blank": 17}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/src/styles/panels.css": {"language": "CSS", "code": 72, "comment": 1, "blank": 15}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/src/styles/index.css": {"language": "CSS", "code": 366, "comment": 10, "blank": 84}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/src/styles/messages.css": {"language": "CSS", "code": 282, "comment": 4, "blank": 46}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/src/styles/messaging.css": {"language": "CSS", "code": 2686, "comment": 56, "blank": 467}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/src/styles/forms.css": {"language": "CSS", "code": 210, "comment": 2, "blank": 34}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/src/styles/emoji-picker.css": {"language": "CSS", "code": 91, "comment": 8, "blank": 17}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/src/styles/global.css": {"language": "CSS", "code": 88, "comment": 2, "blank": 19}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/src/styles/dashboard.css": {"language": "CSS", "code": 320, "comment": 13, "blank": 66}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/src/styles/groupchat.css": {"language": "CSS", "code": 3433, "comment": 47, "blank": 567}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/src/styles/contacts.css": {"language": "CSS", "code": 787, "comment": 9, "blank": 131}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/src/styles/auth.css": {"language": "CSS", "code": 81, "comment": 0, "blank": 9}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/src/styles/category-manager.css": {"language": "CSS", "code": 1159, "comment": 31, "blank": 203}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/src/App.js": {"language": "JavaScript", "code": 7, "comment": 0, "blank": 3}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/src/styles/adminmaster.css": {"language": "CSS", "code": 1537, "comment": 19, "blank": 299}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/src/styles/admin.css": {"language": "CSS", "code": 1752, "comment": 32, "blank": 329}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/src/components/UrgencySelector.js": {"language": "JavaScript", "code": 115, "comment": 15, "blank": 12}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/src/components/SignUpForm.js": {"language": "JavaScript", "code": 291, "comment": 10, "blank": 35}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/src/components/SignInForm.js": {"language": "JavaScript", "code": 134, "comment": 3, "blank": 20}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/src/components/Sidebar.js": {"language": "JavaScript", "code": 291, "comment": 14, "blank": 32}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/src/components/TaskManagement.js": {"language": "JavaScript", "code": 821, "comment": 46, "blank": 66}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/src/components/SettingsPage.js": {"language": "JavaScript", "code": 276, "comment": 10, "blank": 24}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/src/components/SecurityPage.js": {"language": "JavaScript", "code": 202, "comment": 14, "blank": 35}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/src/components/SearchBar.js": {"language": "JavaScript", "code": 57, "comment": 0, "blank": 6}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/src/components/MainDashboard.js": {"language": "JavaScript", "code": 87, "comment": 3, "blank": 10}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/src/components/DashboardPage.js": {"language": "JavaScript", "code": 345, "comment": 6, "blank": 28}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/src/components/CategoryManager.js": {"language": "JavaScript", "code": 374, "comment": 17, "blank": 34}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/src/components/GroupChatPage.js": {"language": "JavaScript", "code": 2635, "comment": 261, "blank": 318}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/src/components/ContactsPage.js": {"language": "JavaScript", "code": 379, "comment": 21, "blank": 43}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/src/components/CategoryDropdown.js": {"language": "JavaScript", "code": 84, "comment": 2, "blank": 13}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/src/components/MessagingPage.js": {"language": "JavaScript", "code": 2603, "comment": 263, "blank": 311}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/src/components/AuthContainer.js": {"language": "JavaScript", "code": 95, "comment": 8, "blank": 11}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/src/components/AdminPanel.js": {"language": "JavaScript", "code": 1127, "comment": 31, "blank": 76}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/src/components/AdminMaster.js": {"language": "JavaScript", "code": 1090, "comment": 33, "blank": 78}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/src/App.css": {"language": "CSS", "code": 0, "comment": 0, "blank": 1}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/backend/task_routes.py": {"language": "Python", "code": 122, "comment": 32, "blank": 34}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/backend/requirements.txt": {"language": "pip requirements", "code": 12, "comment": 0, "blank": 1}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/backend/README.md": {"language": "<PERSON><PERSON>", "code": 62, "comment": 0, "blank": 23}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/package-lock.json": {"language": "JSON", "code": 17884, "comment": 0, "blank": 1}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/backend/file_utils.py": {"language": "Python", "code": 80, "comment": 15, "blank": 23}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/backend/quick_reply_model.log": {"language": "log", "code": 485, "comment": 0, "blank": 31}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/backend/file_upload.py": {"language": "Python", "code": 152, "comment": 35, "blank": 47}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/backend/models.py": {"language": "Python", "code": 33, "comment": 0, "blank": 3}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/backend/encryption.py": {"language": "Python", "code": 88, "comment": 17, "blank": 24}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/backend/category_routes.py": {"language": "Python", "code": 296, "comment": 40, "blank": 81}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/backend/ai_quick_replies.log": {"language": "log", "code": 0, "comment": 0, "blank": 1}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/backend/app.py": {"language": "Python", "code": 1819, "comment": 300, "blank": 474}, "file:///c%3A/PFE_PROJECT/ReactJs/elite/backend/.env": {"language": "Properties", "code": 3, "comment": 0, "blank": 1}}