import os
from flask import Flask, request, render_template, url_for, jsonify, send_from_directory
from flask_socketio import <PERSON><PERSON><PERSON>, emit
from werkzeug.utils import secure_filename
import base64
import uuid
import time

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key'

# File upload settings
UPLOAD_FOLDER = 'uploads'
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'mp4', 'pdf', 'doc', 'docx', 'txt', 'zip'}
MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB max file size

# Create upload directory if it doesn't exist
if not os.path.exists(UPLOAD_FOLDER):
    os.makedirs(UPLOAD_FOLDER)

app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['MAX_CONTENT_LENGTH'] = MAX_CONTENT_LENGTH

# Initialize SocketIO
socketio = SocketIO(app, cors_allowed_origins="*", max_http_buffer_size=16*1024*1024)

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.route('/')
def index():
    return render_template('index.html')

# Handle file uploads
@app.route('/upload', methods=['POST'])
def upload_file():
    if 'file' not in request.files:
        return jsonify({'error': 'No file part'}), 400
        
    file = request.files['file']
    
    if file.filename == '':
        return jsonify({'error': 'No selected file'}), 400
        
    if file and allowed_file(file.filename):
        # Create unique filename to prevent overwriting
        filename = secure_filename(file.filename)
        unique_filename = f"{int(time.time())}_{uuid.uuid4().hex[:8]}_{filename}"
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], unique_filename)
        
        file.save(file_path)
        
        # Determine file type (image, video, document)
        file_extension = filename.rsplit('.', 1)[1].lower()
        if file_extension in ['png', 'jpg', 'jpeg', 'gif']:
            file_type = 'image'
        elif file_extension in ['mp4']:
            file_type = 'video'
        else:
            file_type = 'document'
        
        # Get file size
        file_size = os.path.getsize(file_path)
        
        file_info = {
            'filename': filename,
            'stored_filename': unique_filename,
            'file_type': file_type,
            'file_size': file_size,
            'url': url_for('get_file', filename=unique_filename, _external=True)
        }
        
        return jsonify(file_info), 200
    
    return jsonify({'error': 'File type not allowed'}), 400

# Route to serve uploaded files
@app.route('/uploads/<filename>')
def get_file(filename):
    return send_from_directory(app.config['UPLOAD_FOLDER'], filename)

# Socket.IO event handlers
@socketio.on('connect')
def handle_connect():
    print('Client connected')
    
@socketio.on('disconnect')
def handle_disconnect():
    print('Client disconnected')

@socketio.on('message')
def handle_message(data):
    # Broadcast the message to all connected clients
    emit('message', data, broadcast=True, include_self=False)

@socketio.on('file_message')
def handle_file_message(data):
    # Broadcast the file message to all connected clients
    emit('file_message', data, broadcast=True, include_self=False)

if __name__ == '__main__':
    socketio.run(app, debug=True)