{"name": "sign-in-form", "version": "0.1.0", "private": true, "dependencies": {"@fortawesome/fontawesome-free": "^6.7.2", "@giphy/js-fetch-api": "^5.6.0", "@giphy/react-components": "^10.0.1", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^13.5.0", "axios": "^1.8.4", "crypto-js": "^4.2.0", "emoji-picker-react": "^4.12.2", "react": "^19.1.0", "react-color": "^2.19.3", "react-dom": "^19.1.0", "react-helmet": "^6.1.0", "react-router-dom": "^6.20.0", "react-scripts": "5.0.1", "sign-in-form": "file:", "socket.io-client": "^4.8.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}