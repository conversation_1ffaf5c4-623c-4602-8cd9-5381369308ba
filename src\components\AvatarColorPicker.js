import React, { useState } from "react";
import "../styles/avatar-color-picker.css";

const AvatarColorPicker = ({ 
  isOpen, 
  onClose, 
  onColorSelect, 
  currentColor, 
  contactName,
  isGroup = false 
}) => {
  const [selectedColor, setSelectedColor] = useState(currentColor);

  // Available colors for avatar customization
  const avatarColors = [
    "#FF5733", // Orange/Red
    "#33FF57", // Green
    "#3357FF", // Blue
    "#F333FF", // Purple
    "#FF33F3", // Pink
    "#33FFF3", // <PERSON>an
    "#FFD700", // Gold
    "#9370DB", // Medium Purple
    "#20B2AA", // Light Sea Green
    "#FF6347", // Tomato
    "#4682B4", // Steel Blue
    "#32CD32", // Lime Green
    "#E91E63", // Pink
    "#9C27B0", // Purple
    "#673AB7", // Deep Purple
    "#3F51B5", // Indigo
    "#2196F3", // Blue
    "#03A9F4", // Light Blue
    "#00BCD4", // <PERSON>an
    "#009688", // Teal
    "#4CAF50", // Green
    "#8BC34A", // Light Green
    "#CDDC39", // Lime
    "#FFEB3B", // Yellow
    "#FF9800", // Orange
    "#795548", // Brown
    "#607D8B", // Blue Grey
    "#F44336", // Red
    "#8BC34A", // Light Green
    "#FFC107", // Amber
    "#FF5722", // Deep Orange
    "#9E9E9E", // Grey
  ];

  const handleColorSelect = (color) => {
    setSelectedColor(color);
  };

  const handleSave = () => {
    onColorSelect(selectedColor);
    onClose();
  };

  const handleCancel = () => {
    setSelectedColor(currentColor);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="avatar-color-picker-overlay">
      <div className="avatar-color-picker-modal">
        <div className="avatar-color-picker-header">
          <h3>
            <i className={`fas ${isGroup ? 'fa-users' : 'fa-user'}`}></i>
            Customize {isGroup ? 'Group' : 'Contact'} Avatar Color
          </h3>
          <button className="close-picker-btn" onClick={handleCancel}>
            <i className="fas fa-times"></i>
          </button>
        </div>

        <div className="avatar-color-picker-content">
          <div className="avatar-preview-section">
            <div className="avatar-preview-label">Preview:</div>
            <div 
              className="avatar-preview"
              style={{ backgroundColor: selectedColor }}
            >
              {contactName ? contactName.charAt(0).toUpperCase() : '?'}
            </div>
            <div className="avatar-preview-name">{contactName}</div>
          </div>

          <div className="color-selection-section">
            <div className="color-selection-label">Choose a color:</div>
            <div className="color-grid">
              {avatarColors.map((color) => (
                <div
                  key={color}
                  className={`color-option ${selectedColor === color ? 'selected' : ''}`}
                  style={{ backgroundColor: color }}
                  onClick={() => handleColorSelect(color)}
                  title={color}
                >
                  {selectedColor === color && (
                    <i className="fas fa-check"></i>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className="avatar-color-picker-actions">
          <button className="btn-cancel" onClick={handleCancel}>
            Cancel
          </button>
          <button className="btn-save" onClick={handleSave}>
            Save Color
          </button>
        </div>
      </div>
    </div>
  );
};

export default AvatarColorPicker;
