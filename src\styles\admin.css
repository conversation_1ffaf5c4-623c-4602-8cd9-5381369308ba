/* Modern Admin Panel Styling */
.admin-container {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
  height: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.admin-header {
  margin-bottom: 32px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.admin-header h1 {
  font-size: 2rem;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.dark-mode .admin-header h1 {
  color: #e9e9e9;
}

.admin-actions {
  display: flex;
  gap: 12px;
}

.btn-modern {
  padding: 10px 18px;
  border-radius: 8px;
  font-weight: 500;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 8px;
  border: none;
  background-color: #4a6cf7;
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 5px rgba(74, 108, 247, 0.2);
}

.btn-modern:hover {
  background-color: #3a5ce5;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(74, 108, 247, 0.3);
}

.btn-modern.secondary {
  background-color: #f0f0f0;
  color: #333;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.btn-modern.secondary:hover {
  background-color: #e5e5e5;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn-modern.danger {
  background-color: #f55252;
  box-shadow: 0 2px 5px rgba(245, 82, 82, 0.2);
}

.btn-modern.danger:hover {
  background-color: #e53e3e;
  box-shadow: 0 4px 8px rgba(245, 82, 82, 0.3);
}

.dark-mode .btn-modern.secondary {
  background-color: #3a3a48;
  color: #e9e9e9;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.dark-mode .btn-modern.secondary:hover {
  background-color: #454560;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.admin-tab-container {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.dark-mode .admin-tab-container {
  background-color: #2d2d3a;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
}

.admin-tabs-container {
  margin-bottom: 30px;
}

.admin-tabs {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
  margin-bottom: 20px;
  justify-content: flex-start;
  align-items: center;
}

.admin-tab-content {
  min-height: 600px;
  width: 100%;
  position: relative;
  overflow: hidden;
}

/* Ensure all tab content takes full width */
.admin-tab-content > * {
  width: 100%;
  max-width: 100%;
}

/* Stats grid full width */
.stats-grid {
  width: 100%;
  max-width: 100%;
}

/* User table container full width */
.users-table-container,
.user-table-container {
  width: 100%;
  max-width: 100%;
  overflow-x: auto;
}

/* Messages container full width */
.messages-container,
.messages-container-admin {
  width: 100%;
  max-width: 100%;
}

/* Analytics container full width */
.analytics-container {
  width: 100%;
  max-width: 100%;
}

/* Chart container full width */
.chart-container {
  width: 100%;
  max-width: 100%;
}

/* Admin page animations */
.admin-tab-content > * {
  animation: adminContentSlideIn 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94)
    forwards;
  opacity: 0;
  transform: translateY(20px);
}

@keyframes adminContentSlideIn {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Staggered animations for admin cards */
.stats-grid .stat-card:nth-child(1) {
  animation: adminCardSlideIn 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  animation-delay: 0.1s;
  opacity: 0;
  transform: translateX(-30px);
}

.stats-grid .stat-card:nth-child(2) {
  animation: adminCardSlideIn 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  animation-delay: 0.2s;
  opacity: 0;
  transform: translateX(-30px);
}

.stats-grid .stat-card:nth-child(3) {
  animation: adminCardSlideIn 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  animation-delay: 0.3s;
  opacity: 0;
  transform: translateX(-30px);
}

.stats-grid .stat-card:nth-child(4) {
  animation: adminCardSlideIn 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  animation-delay: 0.4s;
  opacity: 0;
  transform: translateX(-30px);
}

@keyframes adminCardSlideIn {
  0% {
    opacity: 0;
    transform: translateX(-30px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

/* User table animation */
.user-table-container {
  animation: adminTableSlideUp 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94)
    forwards;
  animation-delay: 0.5s;
  opacity: 0;
  transform: translateY(30px);
}

@keyframes adminTableSlideUp {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Messages container animation */
.messages-container-admin {
  animation: adminMessagesSlideIn 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94)
    forwards;
  animation-delay: 0.3s;
  opacity: 0;
  transform: translateY(20px);
}

@keyframes adminMessagesSlideIn {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Analytics container animation */
.analytics-container {
  animation: adminAnalyticsSlideIn 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94)
    forwards;
  animation-delay: 0.2s;
  opacity: 0;
  transform: translateY(20px);
}

@keyframes adminAnalyticsSlideIn {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Admin tab buttons drop-in animation */
@keyframes adminButtonDropIn {
  0% {
    opacity: 0;
    transform: translateY(-50px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Staggered delays for each admin tab button */
.admin-tab-button:nth-child(1) {
  animation-delay: 0.1s;
}

.admin-tab-button:nth-child(2) {
  animation-delay: 0.2s;
}

.admin-tab-button:nth-child(3) {
  animation-delay: 0.3s;
}

.admin-tab-button:nth-child(4) {
  animation-delay: 0.4s;
}

.admin-tab-button {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  background-color: #f8f9fa;
  color: #555;
  border: none;
  border-radius: 12px;
  padding: 20px;
  width: 150px;
  height: 130px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
  /* Drop-in animation */
  animation: adminButtonDropIn 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94)
    forwards;
  opacity: 0;
  transform: translateY(-50px);
}

.dark-mode .admin-tab-button {
  background-color: #2d2d3a;
  color: #e9e9e9;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

.admin-tab-button:hover {
  background-color: #e9ecef;
  transform: translateY(-5px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.admin-tab-button {
  position: relative;
  overflow: hidden;
  background-size: 300% auto;
  transition: all 0.3s ease;
}

/* Apply the NexMessage animation to all admin buttons */
.admin-tab-button {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
}

.dark-mode .admin-tab-button {
  background-color: #2d2d3a;
  border: 1px solid #3a3a48;
}

.admin-tab-button i,
.admin-tab-button span {
  background: linear-gradient(
    90deg,
    #4481eb,
    #6a82fb,
    #05c1ff,
    #6a82fb,
    #4481eb
  );
  background-size: 300% auto;
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  position: relative;
  z-index: 2;
  animation: siri-glow 8s ease infinite;
  text-shadow: 0 0 5px rgba(68, 129, 235, 0.1);
}

/* Dark mode version with stronger glow */
.dark-mode .admin-tab-button i,
.dark-mode .admin-tab-button span {
  background: linear-gradient(
    90deg,
    #00c6ff,
    /* Electric blue */ #b14aff,
    /* Brighter violet for dark mode */ #ff3a8c,
    /* Magenta */ #b14aff,
    /* Brighter violet for dark mode */ #00c6ff /* Electric blue */
  );
  background-size: 300% auto;
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  animation: siri-glow-dark 8s ease infinite;
  text-shadow: 0 0 5px rgba(0, 198, 255, 0.15);
  opacity: 0.9;
}

/* Add the glow effect */
.admin-tab-button .glow-effect {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  border-radius: 12px;
  filter: blur(10px);
  background: inherit;
  background-clip: text;
  -webkit-background-clip: text;
  opacity: 0.7;
  animation: word-glow 3s ease-in-out infinite alternate;
}

.dark-mode .admin-tab-button .glow-effect {
  animation: word-glow-dark 3s ease-in-out infinite alternate;
}

.admin-tab-button i::after,
.admin-tab-button span::after {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  filter: blur(10px);
  background: inherit;
  background-clip: text;
  -webkit-background-clip: text;
  opacity: 0.7;
  animation: word-glow 3s ease-in-out infinite alternate;
}

.dark-mode .admin-tab-button i::after,
.dark-mode .admin-tab-button span::after {
  animation: word-glow-dark 3s ease-in-out infinite alternate;
  opacity: 0.6;
  filter: blur(8px);
}

/* Use the exact same animations as in sidebar.css */
@keyframes siri-glow {
  0% {
    background-position: 0% 50%;
    filter: brightness(1);
  }
  50% {
    background-position: 100% 50%;
    filter: brightness(1.2);
  }
  100% {
    background-position: 0% 50%;
    filter: brightness(1);
  }
}

@keyframes word-glow {
  0% {
    filter: blur(8px);
    opacity: 0.6;
  }
  50% {
    filter: blur(12px);
    opacity: 0.8;
  }
  100% {
    filter: blur(8px);
    opacity: 0.6;
  }
}

/* Animation for dark mode */
@keyframes siri-glow-dark {
  0% {
    background-position: 0% 50%;
    filter: brightness(1);
  }
  50% {
    background-position: 100% 50%;
    filter: brightness(1.2);
  }
  100% {
    background-position: 0% 50%;
    filter: brightness(1);
  }
}

@keyframes word-glow-dark {
  0% {
    filter: blur(8px);
    opacity: 0.5;
  }
  50% {
    filter: blur(12px);
    opacity: 0.7;
  }
  100% {
    filter: blur(8px);
    opacity: 0.5;
  }
}

/* Users button bubble counter animation */
.admin-tab-button .stat-bubble {
  position: relative;
  overflow: hidden;
  z-index: 2;
}

.admin-tab-button .stat-bubble::after {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.3) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: rotate(45deg);
  animation: admin-bubble-shine 3s ease-in-out infinite;
}

@keyframes admin-bubble-shine {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
}

.admin-tab-button::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    145deg,
    rgba(74, 108, 247, 0.1) 0%,
    rgba(255, 255, 255, 0) 60%
  );
  border-radius: 12px;
  z-index: 0;
  animation: admin-tab-wave-ripple 5s ease-in-out infinite;
}

.admin-tab-button:hover i {
  transform: scale(1.03);
  /* Softer glow effect */
  text-shadow: 0 0 5px rgba(74, 108, 247, 0.2);
}

.dark-mode .admin-tab-button:hover i {
  text-shadow: 0 0 5px rgba(0, 198, 255, 0.2);
}

@keyframes admin-wave-shine {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
    opacity: 0.3;
  }
  50% {
    opacity: 0.8;
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
    opacity: 0.3;
  }
}

@keyframes admin-wave-pulse {
  0% {
    opacity: 0.5;
    width: 200%;
    height: 200%;
  }
  50% {
    opacity: 0.9;
    width: 240%;
    height: 240%;
  }
  100% {
    opacity: 0.5;
    width: 200%;
    height: 200%;
  }
}

@keyframes admin-wave-ripple {
  0% {
    transform: scale(1);
    opacity: 0.5;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.9;
  }
  100% {
    transform: scale(1);
    opacity: 0.5;
  }
}

/* Special animations for admin tab buttons */
/* Users button animation */
@keyframes admin-tab-users-shine {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
    opacity: 0.2;
  }
  50% {
    opacity: 0.8;
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
    opacity: 0.2;
  }
}

/* Messages button animation */
@keyframes admin-tab-messages-shine {
  0% {
    transform: translateX(-100%) translateY(100%) rotate(-45deg);
    opacity: 0.3;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    transform: translateX(100%) translateY(-100%) rotate(-45deg);
    opacity: 0.3;
  }
}

/* Group messages button animation */
@keyframes admin-tab-groups-shine {
  0% {
    transform: translateX(0%) translateY(-100%);
    opacity: 0.2;
  }
  50% {
    opacity: 0.6;
    transform: translateX(0%) translateY(0%);
  }
  100% {
    transform: translateX(0%) translateY(100%);
    opacity: 0.2;
  }
}

/* Analytics button animation */
@keyframes admin-tab-analytics-shine {
  0% {
    transform: translateX(-100%) translateY(0%) rotate(0deg);
    opacity: 0.2;
  }
  50% {
    opacity: 0.7;
    transform: translateX(0%) translateY(0%) rotate(180deg);
  }
  100% {
    transform: translateX(100%) translateY(0%) rotate(360deg);
    opacity: 0.2;
  }
}

/* Shared pulse animations with variations */
@keyframes admin-tab-wave-pulse {
  0% {
    opacity: 0.5;
    width: 200%;
    height: 200%;
  }
  50% {
    opacity: 0.9;
    width: 240%;
    height: 240%;
  }
  100% {
    opacity: 0.5;
    width: 200%;
    height: 200%;
  }
}

@keyframes admin-tab-wave-ripple {
  0% {
    transform: scale(1);
    opacity: 0.3;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.5;
  }
  100% {
    transform: scale(1);
    opacity: 0.3;
  }
}

@keyframes admin-icon-glow {
  0% {
    text-shadow: 0 0 3px rgba(74, 108, 247, 0.2);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05), 0 0 5px rgba(68, 129, 235, 0.1);
  }
  50% {
    text-shadow: 0 0 5px rgba(74, 108, 247, 0.3),
      0 0 2px rgba(74, 108, 247, 0.2);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1), 0 0 8px rgba(68, 129, 235, 0.2);
  }
  100% {
    text-shadow: 0 0 3px rgba(74, 108, 247, 0.2);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05), 0 0 5px rgba(68, 129, 235, 0.1);
  }
}

.dark-mode .admin-tab-button:hover {
  background-color: #3a3a48;
}

.admin-tab-button.active {
  background-color: #4a6cf7;
  color: white;
}

.dark-mode .admin-tab-button.active {
  background-color: #5a77ff;
}

.admin-tab-button.active i,
.admin-tab-button.active span {
  background: linear-gradient(
    90deg,
    #ffffff,
    #f0f0f0,
    #ffffff,
    #f0f0f0,
    #ffffff
  );
  background-size: 300% auto;
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  position: relative;
  z-index: 2;
  animation: siri-glow 8s ease infinite;
  text-shadow: 0 0 5px rgba(255, 255, 255, 0.3);
}

.dark-mode .admin-tab-button.active i,
.dark-mode .admin-tab-button.active span {
  background: linear-gradient(
    90deg,
    #ffffff,
    #f0f0f0,
    #ffffff,
    #f0f0f0,
    #ffffff
  );
  background-size: 300% auto;
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  animation: siri-glow-dark 8s ease infinite;
  text-shadow: 0 0 5px rgba(255, 255, 255, 0.3);
}

.admin-tab-button.active:hover {
  background-color: #385de0;
}

.dark-mode .admin-tab-button.active:hover {
  background-color: #4a66e0;
}

.admin-tab-button i {
  font-size: 2rem;
  margin-bottom: 10px;
  position: relative;
  z-index: 2;
  transition: all 0.3s ease;
}

.admin-tab-button i.fa-users-group {
  color: #4a6cf7;
}

.dark-mode .admin-tab-button i.fa-users-group {
  color: #5a77ff;
}

.admin-tab-button.active i.fa-users-group {
  color: white;
}

.admin-panel-header {
  margin-bottom: 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.admin-panel-header h1 {
  font-size: 2rem;
  margin-bottom: 10px;
  color: #333;
}

.dark-mode .admin-panel-header h1 {
  color: #e9e9e9;
}

.admin-panel-header p {
  color: #6c757d;
  font-size: 1rem;
}

.dark-mode .admin-panel-header p {
  color: #a7a7b3;
}

.header-content {
  flex: 1;
}

.refresh-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border-radius: 8px;
  background-color: #4a6cf7;
  color: white;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 5px rgba(74, 108, 247, 0.2);
}

.refresh-button:hover {
  background-color: #3a5ce5;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(74, 108, 247, 0.3);
}

.refresh-button i {
  transition: transform 0.5s ease;
}

.refresh-button:active i {
  transform: rotate(360deg);
}

.dark-mode .refresh-button {
  background-color: #4a6cf7;
  color: white;
}

.dark-mode .refresh-button:hover {
  background-color: #3a5ce5;
}

.admin-panel-container {
  padding: 20px;
  max-width: 1400px;
  min-height: 600px;
  width: 100%;
  margin: 0 auto;
}

.confirm-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.confirm-dialog {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  width: 400px;
  max-width: 90%;
  overflow: hidden;
}

.dark-mode .confirm-dialog {
  background-color: #2d2d3a;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.confirm-dialog-header {
  padding: 20px;
  border-bottom: 1px solid #eaeaea;
}

.dark-mode .confirm-dialog-header {
  border-bottom: 1px solid #3a3a48;
}

.confirm-dialog-header h3 {
  margin: 0;
  font-size: 1.25rem;
  color: #333;
}

.dark-mode .confirm-dialog-header h3 {
  color: #e9e9e9;
}

.confirm-dialog-content {
  padding: 20px;
}

.confirm-dialog-content p {
  margin: 0;
  color: #555;
  line-height: 1.5;
}

.dark-mode .confirm-dialog-content p {
  color: #a7a7b3;
}

.confirm-dialog-actions {
  padding: 15px 20px;
  border-top: 1px solid #eaeaea;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.promote-options {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.admin-role-buttons {
  display: flex;
  gap: 10px;
  justify-content: center;
}

.admin-master-btn {
  background-color: #800080;
  border-color: #800080;
}

.admin-master-btn:hover {
  background-color: #6a006a;
  border-color: #6a006a;
}

.dark-mode .admin-master-btn {
  background-color: #9a009a;
  border-color: #9a009a;
}

.dark-mode .admin-master-btn:hover {
  background-color: #b000b0;
  border-color: #b000b0;
}

.dark-mode .confirm-dialog-actions {
  border-top: 1px solid #3a3a48;
}

.btn {
  padding: 10px 20px;
  border-radius: 6px;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary {
  background-color: #4a6cf7;
  color: white;
}

.btn-primary:hover {
  background-color: #385de0;
}

.btn-secondary {
  background-color: #f0f0f0;
  color: #555;
}

.btn-secondary:hover {
  background-color: #e0e0e0;
}

.btn-danger {
  background-color: #dc3545;
  color: white;
}

.btn-danger:hover {
  background-color: #c82333;
}

.dark-mode .btn-primary {
  background-color: #5a77ff;
}

.dark-mode .btn-primary:hover {
  background-color: #4a66e0;
}

.dark-mode .btn-secondary {
  background-color: #3a3a48;
  color: #e9e9e9;
}

.dark-mode .btn-secondary:hover {
  background-color: #464655;
}

.dark-mode .btn-danger {
  background-color: #e74c3c;
}

.dark-mode .btn-danger:hover {
  background-color: #c0392b;
}

.admin-error {
  background-color: #f8d7da;
  color: #721c24;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}

.admin-error i {
  margin-right: 10px;
}

/* Stats Cards */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background-color: #fff;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  gap: 16px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  position: relative;
  overflow: hidden;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
}

.stat-card::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(74, 108, 247, 0.3) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: rotate(45deg);
  animation: admin-wave-shine 6s linear infinite,
    admin-wave-pulse 8s ease-in-out infinite;
  z-index: 1;
}

.stat-card::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    145deg,
    rgba(74, 108, 247, 0.25) 0%,
    rgba(255, 255, 255, 0) 60%
  );
  border-radius: 12px;
  z-index: 0;
  animation: admin-wave-ripple 10s ease-in-out infinite;
}

.dark-mode .stat-card {
  background-color: #35354a;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.dark-mode .stat-card:hover {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
}

.stat-icon {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  background-color: rgba(74, 108, 247, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: #4a6cf7;
  position: relative;
  z-index: 2;
  /* No animation or glow effect */
  border: 1px solid rgba(74, 108, 247, 0.2);
  box-shadow: none;
}

.dark-mode .stat-icon {
  background-color: rgba(74, 108, 247, 0.15);
  box-shadow: none;
}

.stat-details {
  flex: 1;
}

.stat-label {
  font-size: 0.85rem;
  color: #6c757d;
  margin-bottom: 5px;
}

.dark-mode .stat-label {
  color: #a7a7b3;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 600;
  color: #333;
}

.dark-mode .stat-value {
  color: #e9e9e9;
}

.stat-change {
  font-size: 0.8rem;
  margin-top: 5px;
}

.stat-increase {
  color: #28a745;
}

.dark-mode .stat-increase {
  color: #48c774;
}

.stat-decrease {
  color: #dc3545;
}

/* User Management */
.user-filters {
  display: flex;
  justify-content: space-between;
  margin-bottom: 24px;
  flex-wrap: wrap;
  gap: 15px;
}

.search-users {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 12px 16px;
  max-width: 400px;
  width: 100%;
  transition: box-shadow 0.2s ease;
}

.search-users:focus-within {
  box-shadow: 0 0 0 2px rgba(74, 108, 247, 0.25);
}

.dark-mode .search-users {
  background-color: #35354a;
}

.search-users i {
  color: #6c757d;
  margin-right: 10px;
}

.dark-mode .search-users i {
  color: #a7a7b3;
}

.search-users input {
  border: none;
  background: transparent;
  width: 100%;
  outline: none;
  color: #333;
}

.dark-mode .search-users input {
  color: #e9e9e9;
}

.search-users input::placeholder {
  color: #adb5bd;
}

.filter-button {
  padding: 12px 16px;
  border: none;
  border-radius: 8px;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.filter-button::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(74, 108, 247, 0.25) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: rotate(45deg);
  animation: admin-wave-shine 6s linear infinite,
    admin-wave-pulse 8s ease-in-out infinite;
  z-index: 1;
  pointer-events: none;
}

.filter-button::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    145deg,
    rgba(255, 255, 255, 0.3) 0%,
    rgba(255, 255, 255, 0) 60%
  );
  border-radius: 8px;
  z-index: 0;
  animation: admin-wave-ripple 10s ease-in-out infinite;
  pointer-events: none;
}

.filter-button:hover {
  background-color: #e9e9e9;
}

.dark-mode .filter-button {
  background-color: #35354a;
  color: #e9e9e9;
}

.dark-mode .filter-button:hover {
  background-color: #454560;
}

/* Tables */
.users-table-container,
.messages-table-container {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  margin-bottom: 24px;
  position: relative;
}

.users-table-container::before,
.messages-table-container::before {
  display: none;
}

.users-table-container::after,
.messages-table-container::after {
  display: none;
}

@media (max-width: 1200px) {
  .users-table-container,
  .messages-table-container {
    overflow-x: auto;
    white-space: nowrap;
  }
}

.dark-mode .users-table-container,
.dark-mode .messages-table-container {
  background-color: #35354a;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.users-table,
.messages-table {
  width: 100%;
  border-collapse: collapse;
  min-width: 800px; /* Ensures table maintains minimum width when scrolling */
}

.users-table th,
.users-table td,
.messages-table th,
.messages-table td {
  padding: 16px;
  text-align: left;
  border-bottom: 1px solid #eaeaea;
  vertical-align: middle;
  height: 72px; /* Fixed height for consistent row alignment */
  box-sizing: border-box;
}

.dark-mode .users-table th,
.dark-mode .users-table td,
.dark-mode .messages-table th,
.dark-mode .messages-table td {
  border-bottom: 1px solid #3a3a48;
}

.users-table th,
.messages-table th {
  font-weight: 500;
  color: #495057;
  background-color: #f8f9fa;
  height: 60px; /* Fixed height for table headers */
}

.dark-mode .users-table th,
.dark-mode .messages-table th {
  color: #e9e9e9;
  background-color: #2d2d3a;
}

.users-table tbody tr:hover,
.messages-table tbody tr:hover {
  background-color: #f8f9fa;
}

.dark-mode .users-table tbody tr:hover,
.dark-mode .messages-table tbody tr:hover {
  background-color: #2d2d3a;
}

.user-info,
.message-user {
  display: flex;
  align-items: center;
  gap: 12px;
  height: 100%;
  min-height: 40px;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 18px;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 16px;
  flex-shrink: 0;
  position: relative;
  overflow: hidden;
  animation: admin-avatar-glow 4s ease-in-out infinite;
  border: 2px solid rgba(74, 108, 247, 0.3);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15), 0 0 12px rgba(68, 129, 235, 0.35);
}

.user-avatar::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(74, 108, 247, 0.3) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: rotate(45deg);
  animation: admin-wave-shine 6s linear infinite,
    admin-wave-pulse 8s ease-in-out infinite;
  z-index: 1;
}

.user-avatar::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    145deg,
    rgba(74, 108, 247, 0.25) 0%,
    rgba(255, 255, 255, 0) 60%
  );
  border-radius: 18px;
  z-index: 0;
  animation: admin-wave-ripple 10s ease-in-out infinite;
}

@keyframes admin-wave-shine {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
    opacity: 0.3;
  }
  50% {
    opacity: 0.8;
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
    opacity: 0.3;
  }
}

@keyframes admin-avatar-glow {
  0% {
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15),
      0 0 12px rgba(68, 129, 235, 0.35);
  }
  50% {
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.2), 0 0 20px rgba(68, 129, 235, 0.6);
  }
  100% {
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15),
      0 0 12px rgba(68, 129, 235, 0.35);
  }
}

@keyframes admin-wave-pulse {
  0% {
    opacity: 0.5;
    width: 200%;
    height: 200%;
  }
  50% {
    opacity: 0.9;
    width: 240%;
    height: 240%;
  }
  100% {
    opacity: 0.5;
    width: 200%;
    height: 200%;
  }
}

@keyframes admin-wave-ripple {
  0% {
    transform: scale(1);
    opacity: 0.5;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.9;
  }
  100% {
    transform: scale(1);
    opacity: 0.5;
  }
}

.user-avatar.small {
  width: 32px;
  height: 32px;
  font-size: 12px;
}

.user-role {
  display: inline-flex;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 500;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.role-admin {
  background-color: rgba(255, 140, 0, 0.15);
  color: #ff8c00;
  --role-color: rgba(255, 140, 0, 0.4);
}

.role-admin-master {
  background-color: rgba(128, 0, 128, 0.15);
  color: #800080;
  --role-color: rgba(128, 0, 128, 0.4);
}

.dark-mode .role-admin {
  background-color: rgba(255, 140, 0, 0.2);
  color: #ffa333;
  --role-color: rgba(255, 140, 0, 0.5);
}

.dark-mode .role-admin-master {
  background-color: rgba(128, 0, 128, 0.2);
  color: #d070d0;
  --role-color: rgba(128, 0, 128, 0.5);
}

.role-user {
  background-color: rgba(0, 184, 184, 0.15);
  color: #00b8b8;
  --role-color: rgba(0, 184, 184, 0.4);
}

.dark-mode .role-user {
  background-color: rgba(0, 184, 184, 0.2);
  color: #20d0d0;
  --role-color: rgba(0, 184, 184, 0.5);
}

/* Wave animation for role badge */
.user-role::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent 0%,
    var(--role-color, rgba(255, 255, 255, 0.4)) 50%,
    transparent 100%
  );
  opacity: 0;
  pointer-events: none;
  animation: adminRoleWaveGlow 5s infinite;
  border-radius: 20px;
  clip-path: inset(0 0 0 0 round 20px);
  z-index: 1;
}

/* Internal glow effect */
.user-role::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--role-color, rgba(255, 255, 255, 0.2));
  opacity: 0;
  border-radius: 20px;
  pointer-events: none;
  animation: adminRolePulseGlow 4s infinite;
  clip-path: inset(0 0 0 0 round 20px);
  z-index: 0;
}

/* Wave animation for role glow */
@keyframes adminRoleWaveGlow {
  0% {
    opacity: 0;
    transform: translateX(-50%);
  }
  50% {
    opacity: 0.9;
    transform: translateX(0%);
  }
  100% {
    opacity: 0;
    transform: translateX(50%);
  }
}

/* Pulse animation for role glow */
@keyframes adminRolePulseGlow {
  0% {
    opacity: 0;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 0;
  }
}

/* Role animations defined above */

/* Department styling */
.user-department {
  display: inline-flex;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 500;
  color: white;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.dark-mode .user-department {
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* Wave animation for department badge */
.user-department::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.6) 50%,
    transparent 100%
  );
  opacity: 0;
  pointer-events: none;
  animation: adminDepartmentWaveGlow 5s infinite;
  border-radius: 20px;
  clip-path: inset(0 0 0 0 round 20px);
  z-index: 1;
}

/* Internal glow effect */
.user-department::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.3);
  opacity: 0;
  border-radius: 20px;
  pointer-events: none;
  animation: adminDepartmentPulseGlow 4s infinite;
  clip-path: inset(0 0 0 0 round 20px);
  z-index: 0;
}

/* Wave animation for department glow */
@keyframes adminDepartmentWaveGlow {
  0% {
    opacity: 0;
    transform: translateX(-50%);
  }
  50% {
    opacity: 0.9;
    transform: translateX(0%);
  }
  100% {
    opacity: 0;
    transform: translateX(50%);
  }
}

/* Pulse animation for department glow */
@keyframes adminDepartmentPulseGlow {
  0% {
    opacity: 0;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 0;
  }
}

/* Department-specific colors - matching contacts page */
.user-department[data-department="Human Resources"] {
  background-color: #ff6b6b;
}

.user-department[data-department="Marketing"] {
  background-color: #4ecdc4;
}

.user-department[data-department="Finance"] {
  background-color: #45b7d1;
}

.user-department[data-department="IT"] {
  background-color: #7367f0;
}

.user-department[data-department="Operations"] {
  background-color: #ff9f43;
}

.user-department[data-department="Sales"] {
  background-color: #28c76f;
}

.user-department[data-department="Customer Service"] {
  background-color: #ea5455;
}

.user-department[data-department="Research & Development"] {
  background-color: #9f44d3;
}

.user-department[data-department="Development"] {
  background-color: #5a8dee;
}

.user-department[data-department="Legal"] {
  background-color: #a66a2c;
}

.user-department[data-department="Executive"] {
  background-color: #475f7b;
}

/* Default color for other departments */
.user-department:not([data-department="Human Resources"]):not(
    [data-department="Marketing"]
  ):not([data-department="Finance"]):not([data-department="IT"]):not(
    [data-department="Operations"]
  ):not([data-department="Sales"]):not(
    [data-department="Customer Service"]
  ):not([data-department="Research & Development"]):not(
    [data-department="Development"]
  ):not([data-department="Legal"]):not([data-department="Executive"]) {
  background-color: #4a6cf7;
}

.status-indicator {
  display: inline-flex;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 500;
}

.status-active {
  background-color: rgba(40, 167, 69, 0.15);
  color: #28a745;
}

.status-inactive {
  background-color: rgba(108, 117, 125, 0.15);
  color: #6c757d;
}

.user-actions {
  display: flex;
  gap: 8px;
}

.user-action-button {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  border: none;
  background-color: #f5f5f5;
  color: #6c757d;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.dark-mode .user-action-button {
  background-color: #454560;
  color: #a7a7b3;
}

.user-action-button:hover {
  background-color: #e9e9e9;
  color: #4a6cf7;
}

.dark-mode .user-action-button:hover {
  background-color: #5a5a78;
  color: #5a77ff;
}

.user-action-button.delete:hover {
  background-color: #dc3545;
  color: white;
}

/* Messages Page */
.messages-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.messages-header h3 {
  font-size: 1.3rem;
  margin: 0;
}

.messages-search {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 12px 16px;
  max-width: 400px;
  width: 100%;
}

.dark-mode .messages-search {
  background-color: #35354a;
}

.messages-search i {
  color: #6c757d;
  margin-right: 10px;
}

.messages-search input {
  border: none;
  background: transparent;
  width: 100%;
  outline: none;
  color: #333;
}

.dark-mode .messages-search input {
  color: #e9e9e9;
}

.message-content {
  max-width: 300px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: flex;
  align-items: center;
  height: 100%;
  line-height: 1.4;
}

.encrypted-message {
  color: #888;
  font-style: italic;
  display: flex;
  align-items: center;
  height: 100%;
  width: 100%;
}

.encrypted-icon {
  color: #f44336;
  margin-right: 5px;
}

.dark-mode .encrypted-message {
  color: #aaa;
}

.dark-mode .encrypted-icon {
  color: #ff8a80;
}

/* Message Actions Alignment */
.message-actions {
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 40px;
}

/* Analytics */
.chart-container {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  padding: 24px;
  margin-bottom: 30px;
  position: relative;
  overflow: hidden;
}

.chart-container::before {
  display: none;
}

.chart-container::after {
  display: none;
}

.dark-mode .chart-container {
  background-color: #35354a;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.chart-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
  gap: 10px;
}

.dark-mode .chart-title {
  color: #e9e9e9;
}

.chart-actions {
  display: flex;
  gap: 10px;
}

.chart-action-button {
  padding: 8px 16px;
  border-radius: 20px;
  border: none;
  background-color: #f5f5f5;
  color: #6c757d;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
  text-align: center;
}

.dark-mode .chart-action-button {
  background-color: #454560;
  color: #a7a7b3;
}

.chart-action-button:hover {
  background-color: #e9e9e9;
}

.dark-mode .chart-action-button:hover {
  background-color: #5a5a78;
}

.chart-action-button.active {
  background-color: #4a6cf7;
  color: white;
}

.dark-mode .chart-action-button.active {
  background-color: #5a77ff;
}

.chart-body {
  padding-top: 20px;
}

.modern-chart {
  height: 300px;
  display: flex;
  align-items: flex-end;
  position: relative;
}

.chart-bars {
  display: flex;
  width: 100%;
  justify-content: space-around;
  align-items: flex-end;
  height: 100%;
}

.chart-bar-container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  max-width: 60px;
}

.chart-bar-tooltip {
  position: absolute;
  top: -30px;
  background-color: #333;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  opacity: 0;
  transition: opacity 0.2s ease;
  pointer-events: none;
  white-space: nowrap;
}

.chart-bar-container:hover .chart-bar-tooltip {
  opacity: 1;
}

.chart-bar {
  width: 24px;
  background-color: #4a6cf7;
  border-radius: 4px;
  transition: height 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  box-shadow: 0 0 8px rgba(74, 108, 247, 0.3);
  animation: chart-bar-pulse 3s ease-in-out infinite;
}

@keyframes chart-bar-pulse {
  0% {
    box-shadow: 0 0 8px rgba(74, 108, 247, 0.3);
  }
  50% {
    box-shadow: 0 0 15px rgba(74, 108, 247, 0.5);
  }
  100% {
    box-shadow: 0 0 8px rgba(74, 108, 247, 0.3);
  }
}

.dark-mode .chart-bar {
  background-color: #5a77ff;
}

.chart-label {
  margin-top: 10px;
  font-size: 0.8rem;
  color: #6c757d;
  text-align: center;
}

.dark-mode .chart-label {
  color: #a7a7b3;
}

.chart-bar-value {
  position: absolute;
  top: -25px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 0.75rem;
  color: #6c757d;
  font-weight: 500;
}

.dark-mode .chart-bar-value {
  color: #a7a7b3;
}

/* Chart Legend */
.chart-legend {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-bottom: 20px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 4px;
}

/* Stacked Bar Chart */
.chart-bar-stack {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.chart-bar {
  width: 24px;
  border-radius: 4px;
  transition: height 0.3s ease;
}

.chart-bar.private {
  background-color: #4a90e2;
  box-shadow: 0 0 8px rgba(74, 144, 226, 0.3);
  animation: chart-bar-private-pulse 3s ease-in-out infinite;
}

@keyframes chart-bar-private-pulse {
  0% {
    box-shadow: 0 0 8px rgba(74, 144, 226, 0.3);
  }
  50% {
    box-shadow: 0 0 15px rgba(74, 144, 226, 0.5);
  }
  100% {
    box-shadow: 0 0 8px rgba(74, 144, 226, 0.3);
  }
}

.chart-bar.group {
  background-color: #37bc9b;
  box-shadow: 0 0 8px rgba(55, 188, 155, 0.3);
  animation: chart-bar-group-pulse 3s ease-in-out infinite;
}

@keyframes chart-bar-group-pulse {
  0% {
    box-shadow: 0 0 8px rgba(55, 188, 155, 0.3);
  }
  50% {
    box-shadow: 0 0 15px rgba(55, 188, 155, 0.5);
  }
  100% {
    box-shadow: 0 0 8px rgba(55, 188, 155, 0.3);
  }
}

.chart-bar-tooltip {
  position: absolute;
  top: -65px;
  left: 50%;
  transform: translateX(-50%);
  background-color: #333;
  color: white;
  padding: 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  opacity: 0;
  transition: opacity 0.2s ease;
  pointer-events: none;
  white-space: nowrap;
  z-index: 10;
}

.dark-mode .chart-bar-tooltip {
  background-color: #454560;
}

.chart-bar-container:hover .chart-bar-tooltip {
  opacity: 1;
}

/* Analytics Cards */
.analytics-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.analytics-card {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  position: relative;
}

.analytics-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
}

/* Removed glow effect from main card */
.analytics-card::before {
  content: none;
}

/* Removed glow effect from main card */
.analytics-card::after {
  content: none;
}

.analytics-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
}

.dark-mode .analytics-card {
  background-color: #35354a;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.dark-mode .analytics-card:hover {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
}

.analytics-card-header {
  padding: 16px 20px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #eaeaea;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dark-mode .analytics-card-header {
  background-color: #2d2d3a;
  border-bottom: 1px solid #3a3a48;
}

.analytics-card-header h3 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
}

.dark-mode .analytics-card-header h3 {
  color: #e9e9e9;
}

.analytics-card-header i {
  font-size: 1.2rem;
  color: #4a6cf7;
}

.dark-mode .analytics-card-header i {
  color: #5a77ff;
}

.analytics-card-content {
  padding: 20px;
}

.analytics-stat {
  margin-bottom: 15px;
}

.analytics-stat-value {
  font-size: 1.8rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}

.dark-mode .analytics-stat-value {
  color: #e9e9e9;
}

.analytics-stat-label {
  font-size: 0.85rem;
  color: #6c757d;
}

.dark-mode .analytics-stat-label {
  color: #a7a7b3;
}

.analytics-trend {
  display: flex;
  align-items: center;
  gap: 10px;
}

.analytics-trend-value {
  display: flex;
  align-items: center;
  gap: 5px;
  font-weight: 500;
  color: #28a745;
}

.analytics-trend-value i {
  font-size: 0.9rem;
}

.analytics-trend-label {
  font-size: 0.8rem;
  color: #6c757d;
}

.dark-mode .analytics-trend-label {
  color: #a7a7b3;
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 30px;
  flex-wrap: wrap;
  gap: 15px;
}

.pagination-info {
  font-size: 0.9rem;
  color: #6c757d;
}

.dark-mode .pagination-info {
  color: #a7a7b3;
}

.pagination-controls {
  display: flex;
  gap: 8px;
}

.pagination-button {
  width: 36px;
  height: 36px;
  border-radius: 8px;
  border: none;
  background-color: #f5f5f5;
  color: #6c757d;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
}

.dark-mode .pagination-button {
  background-color: #454560;
  color: #a7a7b3;
}

.pagination-button:hover:not(.disabled) {
  background-color: #e9e9e9;
  color: #4a6cf7;
}

.dark-mode .pagination-button:hover:not(.disabled) {
  background-color: #5a5a78;
  color: #5a77ff;
}

.pagination-button.active {
  background-color: #4a6cf7;
  color: white;
}

.dark-mode .pagination-button.active {
  background-color: #5a77ff;
}

.pagination-button.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Loading State */
.loading-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
}

.loader {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #4a6cf7;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

.dark-mode .loader {
  border: 4px solid #3a3a48;
  border-top: 4px solid #5a77ff;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Modals */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.user-modal {
  background-color: #fff;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.dark-mode .user-modal {
  background-color: #2d2d3a;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.user-modal-header {
  padding: 20px;
  border-bottom: 1px solid #eaeaea;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dark-mode .user-modal-header {
  border-bottom: 1px solid #3a3a48;
}

.user-modal-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
}

.dark-mode .user-modal-title {
  color: #e9e9e9;
}

.user-modal-close {
  background: none;
  border: none;
  font-size: 1.2rem;
  color: #6c757d;
  cursor: pointer;
}

.dark-mode .user-modal-close {
  color: #a7a7b3;
}

.user-modal-body {
  padding: 20px;
}

.user-modal-footer {
  padding: 20px;
  border-top: 1px solid #eaeaea;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.dark-mode .user-modal-footer {
  border-top: 1px solid #3a3a48;
}

/* Responsive Adjustments */
@media (max-width: 1024px) {
  .stats-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  }

  .analytics-cards {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }
}

@media (max-width: 768px) {
  .admin-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .admin-tabs {
    overflow-x: auto;
    padding: 0;
  }

  .admin-tab {
    padding: 15px 16px;
    white-space: nowrap;
  }

  .chart-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .chart-actions {
    width: 100%;
    justify-content: space-between;
  }

  .user-filters {
    flex-direction: column;
  }

  .search-users,
  .messages-search {
    max-width: 100%;
  }

  .messages-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
}

@media (max-width: 576px) {
  .admin-container {
    padding: 15px;
  }

  .stats-grid,
  .analytics-cards {
    grid-template-columns: 1fr;
  }

  .admin-tab-content {
    padding: 15px;
  }

  .chart-container,
  .setting-card {
    padding: 15px;
  }

  .pagination {
    flex-direction: column;
    align-items: flex-start;
  }

  .pagination-controls {
    width: 100%;
    justify-content: center;
  }

  .user-modal {
    width: 95%;
  }
}

/* Search Styles */
.admin-search.search-bar-wrapper {
  flex: 1;
  max-width: 400px;
}

.admin-search .search-bar {
  background-color: #f5f5f5;
  border-color: #e0e0e0;
  height: 44px;
}

.dark-mode .admin-search .search-bar {
  background-color: #35354a;
  border-color: #3a3a48;
}

.admin-search .search-bar:focus-within {
  border-color: #4a6cf7;
  box-shadow: 0 0 0 2px rgba(74, 108, 247, 0.15);
}

.dark-mode .admin-search .search-bar:focus-within {
  border-color: #5a77ff;
  box-shadow: 0 0 0 2px rgba(90, 119, 255, 0.15);
}

/* Admin search clear button */
.admin-search .search-bar .clear-button {
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s ease;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.dark-mode .admin-search .search-bar .clear-button {
  color: #a7a7b3;
}

.admin-search .search-bar .clear-button:hover {
  color: #4a6cf7;
  transform: rotate(90deg);
  background-color: rgba(74, 108, 247, 0.1);
}

.dark-mode .admin-search .search-bar .clear-button:hover {
  color: #7a9fff;
  background-color: rgba(122, 159, 255, 0.1);
}

/* User Filters Section */
.user-filters {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  gap: 16px;
}

/* Filter Button */
.filter-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border: none;
  border-radius: 8px;
  background-color: #f5f5f5;
  color: #555;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.dark-mode .filter-button {
  background-color: #35354a;
  color: #e9e9e9;
}

.filter-button:hover {
  background-color: #e9e9e9;
}

.dark-mode .filter-button:hover {
  background-color: #454560;
}

.filter-icon {
  font-size: 0.9rem;
  color: #6c757d;
}

.dark-mode .filter-icon {
  color: #a7a7b3;
}

/* Messages Header */
.messages-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  gap: 16px;
}

.messages-header h3 {
  font-size: 1.3rem;
  margin: 0;
  color: #333;
}

.dark-mode .messages-header h3 {
  color: #e9e9e9;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .user-filters {
    flex-direction: column;
    align-items: stretch;
  }

  .admin-search.search-bar-wrapper {
    max-width: none;
  }

  .messages-header {
    flex-direction: column;
    align-items: stretch;
  }
}

.group-name {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #4a6cf7;
}

.dark-mode .group-name {
  color: #5a77ff;
}

.group-name i {
  font-size: 1rem;
}

.group-name span {
  font-weight: 500;
}

/* Analytics Cards */
.analytics-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-top: 24px;
}

.analytics-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.dark-mode .analytics-card {
  background: #2a2a3c;
}

.analytics-card-header {
  margin-bottom: 16px;
}

.analytics-card-header h3 {
  font-size: 1.1rem;
  color: #333;
  display: flex;
  align-items: center;
  gap: 8px;
}

.dark-mode .analytics-card-header h3 {
  color: #e9e9e9;
}

.analytics-card-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.analytics-stat {
  padding: 12px;
  border-radius: 8px;
  background: #f8f9fa;
  position: relative;
  overflow: hidden;
}

/* Apply glow effect only to specific inner elements */
/* User Growth card: Total Users and Active Users sections */
/* Message Activity card: Total Messages, Private, and Group sections */
.analytics-card:nth-child(1) .analytics-stat:nth-child(1)::before,
.analytics-card:nth-child(1) .analytics-stat:nth-child(2)::before,
.analytics-card:nth-child(2) .analytics-stat:nth-child(1)::before,
.analytics-card:nth-child(2) .breakdown-item:nth-child(1)::before,
.analytics-card:nth-child(2) .breakdown-item:nth-child(2)::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(74, 108, 247, 0.3) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: rotate(45deg);
  animation: admin-wave-shine 6s linear infinite,
    admin-wave-pulse 8s ease-in-out infinite;
  z-index: 1;
  pointer-events: none;
}

/* For all other stat elements, remove the glow */
.analytics-stat::before {
  content: none;
}

/* Apply glow effect only to specific inner elements */
/* User Growth card: Total Users and Active Users sections */
/* Message Activity card: Total Messages, Private, and Group sections */
.analytics-card:nth-child(1) .analytics-stat:nth-child(1)::after,
.analytics-card:nth-child(1) .analytics-stat:nth-child(2)::after,
.analytics-card:nth-child(2) .analytics-stat:nth-child(1)::after,
.analytics-card:nth-child(2) .breakdown-item:nth-child(1)::after,
.analytics-card:nth-child(2) .breakdown-item:nth-child(2)::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    145deg,
    rgba(255, 255, 255, 0.3) 0%,
    rgba(255, 255, 255, 0) 60%
  );
  border-radius: 8px;
  z-index: 0;
  animation: admin-wave-ripple 10s ease-in-out infinite;
  pointer-events: none;
}

/* For all other stat elements, remove the glow */
.analytics-stat::after {
  content: none;
}

.dark-mode .analytics-stat {
  background: #353547;
}

/* Dark mode styles for specific inner elements */
.dark-mode .analytics-card:nth-child(1) .analytics-stat:nth-child(1)::before,
.dark-mode .analytics-card:nth-child(1) .analytics-stat:nth-child(2)::before,
.dark-mode .analytics-card:nth-child(2) .analytics-stat:nth-child(1)::before,
.dark-mode .analytics-card:nth-child(2) .breakdown-item:nth-child(1)::before,
.dark-mode .analytics-card:nth-child(2) .breakdown-item:nth-child(2)::before {
  background: linear-gradient(
    45deg,
    rgba(0, 0, 0, 0) 0%,
    rgba(74, 108, 247, 0.3) 50%,
    rgba(0, 0, 0, 0) 100%
  );
}

/* Dark mode styles for specific inner elements */
.dark-mode .analytics-card:nth-child(1) .analytics-stat:nth-child(1)::after,
.dark-mode .analytics-card:nth-child(1) .analytics-stat:nth-child(2)::after,
.dark-mode .analytics-card:nth-child(2) .analytics-stat:nth-child(1)::after,
.dark-mode .analytics-card:nth-child(2) .breakdown-item:nth-child(1)::after,
.dark-mode .analytics-card:nth-child(2) .breakdown-item:nth-child(2)::after {
  background: linear-gradient(
    145deg,
    rgba(74, 108, 247, 0.2) 0%,
    rgba(0, 0, 0, 0) 60%
  );
}

.analytics-stat-value {
  font-size: 2rem;
  font-weight: 600;
  color: #333;
  line-height: 1;
}

.dark-mode .analytics-stat-value {
  color: #e9e9e9;
}

.analytics-stat-label {
  font-size: 0.9rem;
  color: #6c757d;
  margin-top: 4px;
}

.dark-mode .analytics-stat-label {
  color: #a7a7b3;
}

.analytics-stat-secondary {
  font-size: 0.85rem;
  color: #6c757d;
  margin-top: 4px;
}

.analytics-trend {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
}

.analytics-trend-value {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.9rem;
  font-weight: 500;
}

.analytics-trend-value.positive {
  color: #28a745;
}

.analytics-trend-value.negative {
  color: #dc3545;
}

.analytics-trend-label {
  font-size: 0.85rem;
  color: #6c757d;
}

.analytics-breakdown {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-top: 12px;
}

.breakdown-item {
  padding: 12px;
  border-radius: 8px;
  background: #f8f9fa;
  position: relative;
  overflow: hidden;
}

.dark-mode .breakdown-item {
  background: #353547;
}

.breakdown-label {
  font-size: 0.9rem;
  color: #6c757d;
}

.breakdown-value {
  font-size: 1.25rem;
  font-weight: 600;
  color: #333;
  margin-top: 4px;
}

.dark-mode .breakdown-value {
  color: #e9e9e9;
}

.breakdown-percentage {
  font-size: 0.85rem;
  color: #6c757d;
  margin-top: 2px;
}

.success-message {
  background-color: #d4edda;
  color: #155724;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.dark-mode .success-message {
  background-color: #1e3320;
  color: #81c784;
}

.success-message i {
  font-size: 1.2rem;
}
