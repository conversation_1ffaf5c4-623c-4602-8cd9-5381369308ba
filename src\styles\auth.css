.container {
  position: relative;
  width: 100%;
  min-height: 100vh;
  overflow: hidden;
}
.container:before {
  content: "";
  position: absolute;
  height: 2000px;
  width: 2000px;
  top: -10%;
  right: 48%;
  transform: translateY(-50%);
  background-image: linear-gradient(-45deg, #4481eb 0%, #04befe 100%);
  transition: 1.8s ease-in-out;
  border-radius: 50%;
  z-index: 6;
}

.sign-up-mode:before {
  transform: translate(100%, -50%);
  right: 52%;
}
.auth-container {
  transition: all 1.2s ease-in-out;
}

.forms-container {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}

.signin-signup {
  position: absolute;
  top: 50%;
  left: 75%;
  transform: translate(-50%, -50%);
  width: 50%;
  display: grid;
  grid-template-columns: 1fr;
  z-index: 5;
  transition: 1.1s;
}

.sign-up-mode .signin-signup {
  left: 25%;
}

.left-panel .content {
  position: relative;
  top: 65px;
}
.content p{
  position: relative;
  left: -5%   !important;
  font-size: 24px !important;
  font-weight: lighter !important;
  margin-bottom: 25px;
  color: #ffffff;
}

.content{
  width: 90%;
}

.highlight {
  font-weight: bold;
  font-style: italic;
}
.container.sign-up-mode .left-panel .image,
.container.sign-up-mode .left-panel .content {
  transform: translateX(-800px);
}

.image{
  max-width: 70%;
}

.right-panel .image {
  position: relative;
  right: 220px;
}
.container.sign-up-mode .right-panel .image,
.container.sign-up-mode .right-panel .content {
  transform: translateX(0%);
}