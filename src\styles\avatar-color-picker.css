/* Avatar Color Picker Styles */
.avatar-color-picker-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  animation: fade-in 0.3s ease-out;
}

.avatar-color-picker-modal {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow: hidden;
  animation: slide-in-up 0.4s ease-out;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.avatar-color-picker-header {
  background: linear-gradient(135deg, #4A6CF7 0%, #6C5CE7 100%);
  color: white;
  padding: 20px 25px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.avatar-color-picker-header h3 {
  margin: 0;
  font-size: 1.3rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 10px;
}

.close-picker-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  width: 35px;
  height: 35px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  font-size: 1rem;
}

.close-picker-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.avatar-color-picker-content {
  padding: 25px;
  max-height: 60vh;
  overflow-y: auto;
}

.avatar-preview-section {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 15px;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.avatar-preview-label {
  font-size: 1rem;
  font-weight: 600;
  color: #495057;
  margin-bottom: 15px;
}

.avatar-preview {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  font-weight: 700;
  color: white;
  margin: 0 auto 10px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  border: 3px solid rgba(255, 255, 255, 0.3);
}

.avatar-preview-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: #495057;
  margin-top: 10px;
}

.color-selection-section {
  margin-top: 20px;
}

.color-selection-label {
  font-size: 1rem;
  font-weight: 600;
  color: #495057;
  margin-bottom: 15px;
}

.color-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(45px, 1fr));
  gap: 12px;
  max-height: 300px;
  overflow-y: auto;
  padding: 5px;
}

.color-option {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  border: 3px solid transparent;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  position: relative;
  overflow: hidden;
}

.color-option:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
}

.color-option.selected {
  border-color: #ffffff;
  transform: scale(1.15);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.color-option i {
  color: white;
  font-size: 1rem;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}

.avatar-color-picker-actions {
  padding: 20px 25px;
  background: #f8f9fa;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: flex-end;
  gap: 15px;
}

.btn-cancel,
.btn-save {
  padding: 12px 25px;
  border: none;
  border-radius: 10px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 100px;
}

.btn-cancel {
  background: #6c757d;
  color: white;
}

.btn-cancel:hover {
  background: #5a6268;
  transform: translateY(-2px);
}

.btn-save {
  background: linear-gradient(135deg, #4A6CF7 0%, #6C5CE7 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(74, 108, 247, 0.3);
}

.btn-save:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(74, 108, 247, 0.4);
}

/* Dark mode support */
.dark-mode .avatar-color-picker-modal {
  background: linear-gradient(135deg, #2c2c2c 0%, #1a1a1a 100%);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.dark-mode .avatar-preview-section {
  background: linear-gradient(135deg, #3c3c3c 0%, #2c2c2c 100%);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.dark-mode .avatar-preview-label,
.dark-mode .color-selection-label,
.dark-mode .avatar-preview-name {
  color: #e9ecef;
}

.dark-mode .avatar-color-picker-actions {
  background: #2c2c2c;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Animations */
@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slide-in-up {
  from {
    opacity: 0;
    transform: translateY(50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Responsive design */
@media (max-width: 576px) {
  .avatar-color-picker-modal {
    width: 95%;
    margin: 10px;
  }
  
  .avatar-color-picker-header {
    padding: 15px 20px;
  }
  
  .avatar-color-picker-header h3 {
    font-size: 1.1rem;
  }
  
  .avatar-color-picker-content {
    padding: 20px;
  }
  
  .color-grid {
    grid-template-columns: repeat(auto-fit, minmax(40px, 1fr));
    gap: 10px;
  }
  
  .color-option {
    width: 40px;
    height: 40px;
  }
  
  .avatar-preview {
    width: 70px;
    height: 70px;
    font-size: 1.8rem;
  }
}
