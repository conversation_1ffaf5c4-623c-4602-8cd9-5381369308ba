.category-manager {
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 600px;
  max-height: 80vh;
  overflow-y: auto;
  padding: 20px;
  position: relative;
}

.dark-mode .category-manager {
  background-color: #2a2a3c;
  color: #f0f0f0;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.category-manager-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.dark-mode .category-manager-header {
  border-bottom-color: #444;
}

.category-manager-header h2 {
  margin: 0;
  font-size: 1.5rem;
  color: #333;
}

.dark-mode .category-manager-header h2 {
  color: #f0f0f0;
}

.close-button {
  background: none;
  border: none;
  font-size: 1.2rem;
  color: #888;
  cursor: pointer;
  transition: color 0.2s;
}

.close-button:hover {
  color: #333;
}

.dark-mode .close-button:hover {
  color: #fff;
}

.error-message {
  background-color: #ffebee;
  color: #d32f2f;
  padding: 10px;
  border-radius: 5px;
  margin-bottom: 15px;
  font-size: 0.9rem;
}

.dark-mode .error-message {
  background-color: rgba(211, 47, 47, 0.2);
}

.category-form-container {
  margin-bottom: 20px;
}

.category-form {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
}

.dark-mode .category-form {
  background-color: #353547;
}

.category-form h3 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 1.2rem;
  color: #444;
}

.dark-mode .category-form h3 {
  color: #e0e0e0;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #555;
}

.dark-mode .form-group label {
  color: #ccc;
}

.form-group input {
  width: 100%;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-size: 0.95rem;
}

.dark-mode .form-group input {
  background-color: #2a2a3c;
  border-color: #444;
  color: #f0f0f0;
}

.color-picker {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 5px;
}

.color-option {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  cursor: pointer;
  transition: transform 0.2s, box-shadow 0.2s;
}

.color-option:hover {
  transform: scale(1.1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.color-option.selected {
  box-shadow: 0 0 0 2px #fff, 0 0 0 4px #4a76a8;
  transform: scale(1.1);
}

.dark-mode .color-option.selected {
  box-shadow: 0 0 0 2px #2a2a3c, 0 0 0 4px #4a76a8;
}

.form-actions {
  display: flex;
  gap: 10px;
  margin-top: 15px;
}

.btn-primary {
  background-color: #4a76a8;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 5px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
}

.btn-primary:hover {
  background-color: #3a5f8a;
}

.btn-secondary {
  background-color: #f0f0f0;
  color: #333;
  border: 1px solid #ddd;
  padding: 8px 16px;
  border-radius: 5px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
}

.dark-mode .btn-secondary {
  background-color: #444;
  color: #f0f0f0;
  border-color: #555;
}

.btn-secondary:hover {
  background-color: #e0e0e0;
}

.dark-mode .btn-secondary:hover {
  background-color: #555;
}

.categories-list {
  margin-top: 20px;
}

.categories-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  border-bottom: 1px solid #eee;
  padding-bottom: 8px;
}

.categories-list h3 {
  margin-top: 0;
  margin-bottom: 0;
  font-size: 1.2rem;
  color: #444;
}

.delete-all-button {
  background-color: rgba(255, 71, 87, 0.1);
  color: #ff4757;
  border: none;
  border-radius: 6px;
  padding: 5px 10px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 5px;
}

.delete-all-button:hover {
  background-color: rgba(255, 71, 87, 0.2);
  transform: translateY(-1px);
}

.delete-all-button i {
  font-size: 0.75rem;
}

.dark-mode .categories-list-header {
  border-bottom-color: #444;
}

.dark-mode .categories-list h3 {
  color: #e0e0e0;
}

.dark-mode .delete-all-button {
  background-color: rgba(255, 71, 87, 0.15);
  color: #ff6b81;
}

.dark-mode .delete-all-button:hover {
  background-color: rgba(255, 71, 87, 0.25);
}

.categories-list ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.category-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  border-radius: 5px;
  margin-bottom: 8px;
  background-color: #f9f9f9;
  transition: background-color 0.2s;
}

.dark-mode .category-item {
  background-color: #353547;
}

.category-item:hover {
  background-color: #f0f0f0;
}

.dark-mode .category-item:hover {
  background-color: #404055;
}

.category-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.category-color {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  flex-shrink: 0;
}

.category-name {
  font-weight: 500;
  color: #333;
}

.dark-mode .category-name {
  color: #f0f0f0;
}

.category-actions {
  display: flex;
  gap: 8px;
}

.edit-button,
.delete-button {
  background: none;
  border: none;
  font-size: 0.9rem;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.2s ease;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.edit-button {
  color: #4a76a8;
}

.delete-button {
  color: #e64c3c;
}

.edit-button:hover {
  background-color: rgba(74, 118, 168, 0.1);
  transform: translateY(-2px);
  box-shadow: 0 2px 5px rgba(74, 118, 168, 0.2);
}

.delete-button:hover {
  background-color: rgba(230, 76, 60, 0.1);
  transform: translateY(-2px);
  box-shadow: 0 2px 5px rgba(230, 76, 60, 0.2);
}

.dark-mode .edit-button:hover {
  background-color: rgba(74, 118, 168, 0.2);
  box-shadow: 0 2px 5px rgba(74, 118, 168, 0.3);
}

.dark-mode .delete-button:hover {
  background-color: rgba(230, 76, 60, 0.2);
  box-shadow: 0 2px 5px rgba(230, 76, 60, 0.3);
}

.loading,
.no-categories {
  text-align: center;
  padding: 20px;
  color: #888;
  font-style: italic;
}

.dark-mode .loading,
.dark-mode .no-categories {
  color: #aaa;
}

/* Modal overlay for the category manager */
.category-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

/* Confirmation dialog */
.confirm-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1100;
  animation: fadeIn 0.2s ease-out;
}

.confirm-dialog {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
  width: 400px;
  max-width: 90%;
  overflow: hidden;
  animation: scaleIn 0.2s ease-out;
}

.dark-mode .confirm-dialog {
  background-color: #2a2a3c;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
}

.confirm-dialog-header {
  padding: 20px;
  border-bottom: 1px solid #eee;
}

.dark-mode .confirm-dialog-header {
  border-bottom: 1px solid #444;
}

.confirm-dialog-header h3 {
  margin: 0;
  font-size: 1.2rem;
  color: #333;
}

.dark-mode .confirm-dialog-header h3 {
  color: #e9e9e9;
}

.confirm-dialog-content {
  padding: 20px;
}

.confirm-dialog-content p {
  margin: 0;
  color: #555;
  line-height: 1.5;
}

.dark-mode .confirm-dialog-content p {
  color: #a7a7b3;
}

.confirm-dialog-actions {
  padding: 15px 20px;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.dark-mode .confirm-dialog-actions {
  border-top: 1px solid #444;
}

.btn-cancel {
  padding: 8px 16px;
  border-radius: 6px;
  background-color: #f0f0f0;
  color: #555;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.dark-mode .btn-cancel {
  background-color: #3a3a48;
  color: #e0e0e0;
}

.btn-cancel:hover {
  background-color: #e0e0e0;
}

.dark-mode .btn-cancel:hover {
  background-color: #454560;
}

.btn-danger {
  padding: 8px 16px;
  border-radius: 6px;
  background-color: #e64c3c;
  color: white;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-danger:hover {
  background-color: #d44333;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.category-dropdown-menu {
  position: fixed;
  width: 200px;
  max-height: 300px;
  background-color: white;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  z-index: 1000;
  overflow: hidden;
  animation: fadeIn 0.2s ease-out;
}

.dark-mode .category-dropdown-menu {
  background-color: #2a2a3c;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.4);
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Category badge styles */
.category-badge {
  display: inline-flex;
  align-items: center;
  padding: 3px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  margin-left: 8px;
  color: white;
}

/* Category dropdown styles */
.category-dropdown {
  position: relative;
}

.category-dropdown-toggle {
  background: none;
  border: none;
  display: flex;
  align-items: center;
  gap: 5px;
  cursor: pointer;
  padding: 5px;
  border-radius: 3px;
  color: #555;
}

.dark-mode .category-dropdown-toggle {
  color: #ccc;
}

.category-dropdown-menu {
  position: fixed; /* Changed from absolute to fixed for better positioning */
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
  width: 250px;
  z-index: 9999; /* Increased z-index to ensure it's above everything */
  overflow: hidden; /* Hide overflow but don't add scrollbar here */
  max-height: 400px; /* Maximum height */
  animation: fadeIn 0.2s ease-out;
  margin-top: 5px;
  display: flex;
  flex-direction: column;
}

.dark-mode .category-dropdown-menu {
  background-color: #2a2a3c;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.category-dropdown-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #eee;
  background-color: #f9f9f9;
  position: sticky;
  top: 0;
  z-index: 1;
}

.dark-mode .category-dropdown-header {
  border-bottom: 1px solid #444;
  background-color: #232334;
}

.category-dropdown-header h4 {
  margin: 0;
  font-size: 1.1rem;
  color: #333;
  font-weight: 600;
}

.dark-mode .category-dropdown-header h4 {
  color: #f0f0f0;
}

.category-dropdown-close {
  background: none;
  border: none;
  color: #888;
  cursor: pointer;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.category-dropdown-close:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: #333;
}

.dark-mode .category-dropdown-close:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: #fff;
}

.category-dropdown-items {
  max-height: 300px; /* Reduced max height to ensure it fits on screen */
  overflow-y: auto; /* Only this container should have a scrollbar */
  padding: 5px 0;
  width: 100%; /* Ensure full width */
  flex: 1; /* Take up remaining space */
  scrollbar-width: thin; /* For Firefox */
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent; /* For Firefox */
}

/* Custom scrollbar for WebKit browsers */
.category-dropdown-items::-webkit-scrollbar {
  width: 6px;
}

.category-dropdown-items::-webkit-scrollbar-track {
  background: transparent;
}

.category-dropdown-items::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.dark-mode .category-dropdown-items::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.2);
}

/* Only hide scroll buttons in dropdown menus */
.category-dropdown-menu .scroll-left-btn,
.category-dropdown-menu .scroll-right-btn {
  display: none !important;
}

.category-dropdown-section-label {
  padding: 8px 15px 6px;
  font-size: 0.8rem;
  font-weight: 600;
  color: #666;
  margin-top: 8px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.dark-mode .category-dropdown-section-label {
  color: #aaa;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.category-dropdown-item {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  cursor: pointer;
  transition: all 0.2s;
  border-bottom: 1px solid rgba(0, 0, 0, 0.03);
  width: 100%; /* Ensure full width */
  box-sizing: border-box; /* Include padding in width calculation */
  white-space: nowrap; /* Prevent text wrapping */
  overflow: hidden; /* Hide overflow */
  text-overflow: ellipsis; /* Show ellipsis for overflow text */
}

.dark-mode .category-dropdown-item {
  border-bottom: 1px solid rgba(255, 255, 255, 0.03);
}

.category-dropdown-item:hover {
  background-color: #f5f5f5;
}

.dark-mode .category-dropdown-item:hover {
  background-color: #353547;
}

.category-dropdown-item.active {
  background-color: #e6f0fa;
}

.dark-mode .category-dropdown-item.active {
  background-color: #3a4a5f;
}

.category-dropdown-color {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  margin-right: 10px;
  box-shadow: 0 0 3px rgba(0, 0, 0, 0.1);
}

.dark-mode .category-dropdown-color {
  box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);
}

/* Glowing indicator for remove category */
.category-remove-indicator {
  animation: pulseRemoveIndicator 2s infinite;
}

@keyframes pulseRemoveIndicator {
  0% {
    box-shadow: 0 0 3px currentColor;
  }
  50% {
    box-shadow: 0 0 8px currentColor;
  }
  100% {
    box-shadow: 0 0 3px currentColor;
  }
}

.category-dropdown-name {
  flex-grow: 1;
  color: #333;
  font-size: 0.95rem;
  overflow: hidden;
  display: flex;
  flex-direction: column; /* Stack content vertically */
}

/* Allow text to wrap in the remove category option */
.remove-category .category-dropdown-name {
  white-space: normal; /* Allow text to wrap */
  line-height: 1.3; /* Add some spacing between lines */
}

/* Style for each line in the remove category text */
.remove-category-line {
  margin: 2px 0;
  white-space: normal;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Make the category name line stand out */
.remove-category-line:last-child {
  font-weight: 500;
}

/* Style for category name in quotes */
.category-dropdown-name strong,
.category-dropdown-name em {
  font-weight: 600;
  font-style: normal;
}

/* Style for quoted category name */
.category-dropdown-name span {
  color: inherit;
  font-weight: 600;
}

.dark-mode .category-dropdown-name {
  color: #f0f0f0;
}

.category-dropdown-badge {
  font-size: 0.65rem;
  background-color: rgba(0, 0, 0, 0.1);
  padding: 1px 5px;
  border-radius: 8px;
  margin-left: 5px;
  font-weight: 500;
}

.dark-mode .category-dropdown-badge {
  background-color: rgba(255, 255, 255, 0.15);
}

.category-dropdown-selected {
  margin-left: auto;
  color: #4a90e2;
}

.dark-mode .category-dropdown-selected {
  color: #5fa8ff;
}

/* Department dropdown item styling */
.department-dropdown-item {
  cursor: not-allowed;
  opacity: 0.8;
  background-color: rgba(0, 0, 0, 0.02);
}

.dark-mode .department-dropdown-item {
  background-color: rgba(255, 255, 255, 0.03);
}

.department-dropdown-item:hover {
  background-color: rgba(0, 0, 0, 0.03);
  transform: none;
  box-shadow: none;
}

.dark-mode .department-dropdown-item:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

.category-dropdown-manage {
  padding: 8px 12px;
  text-align: center;
  border-top: 1px solid #eee;
  color: #4a76a8;
  font-weight: 500;
  cursor: pointer;
}

.dark-mode .category-dropdown-manage {
  border-top-color: #444;
}

.category-dropdown-manage:hover {
  background-color: #f5f5f5;
}

.dark-mode .category-dropdown-manage:hover {
  background-color: #353547;
}

/* Category filter button */
.category-filter-button {
  background: none;
  border: none;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: rgba(74, 144, 226, 0.1);
}

.dark-mode .category-filter-button {
  background-color: rgba(74, 144, 226, 0.15);
}

.category-filter-button i {
  font-size: 16px;
  color: #4a90e2;
  filter: drop-shadow(0 0 3px rgba(74, 144, 226, 0.3));
  transition: all 0.3s ease;
}

.dark-mode .category-filter-button i {
  color: #5fa8ff;
  filter: drop-shadow(0 0 4px rgba(95, 168, 255, 0.4));
}

.category-filter-button:hover {
  background-color: rgba(74, 144, 226, 0.2);
  transform: translateY(-1px);
}

.dark-mode .category-filter-button:hover {
  background-color: rgba(74, 144, 226, 0.25);
}

.category-filter-button:hover i {
  filter: drop-shadow(0 0 5px rgba(74, 144, 226, 0.5));
}

.dark-mode .category-filter-button:hover i {
  filter: drop-shadow(0 0 6px rgba(95, 168, 255, 0.6));
}

/* Category filter styles */
.category-filter-wrapper {
  margin-bottom: 15px;
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: 8px;
  padding: 10px;
}

.dark-mode .category-filter-wrapper {
  background-color: rgba(255, 255, 255, 0.03);
}

.category-filter-header {
  margin-bottom: 10px;
  display: flex;
  justify-content: center;
}

.category-filter-sections {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.category-filter-section {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.category-section-label {
  font-size: 0.75rem;
  color: #666;
  margin-left: 5px;
  font-weight: 500;
}

.dark-mode .category-section-label {
  color: #aaa;
}

.all-filter-item {
  display: inline-flex;
  margin-bottom: 5px;
  font-size: 0.8rem;
  padding: 3px 8px;
  height: 24px;
}

.category-filter-hr {
  border: 0;
  height: 1px;
  background-color: rgba(0, 0, 0, 0.1);
  margin: 5px 0;
}

.dark-mode .category-filter-hr {
  background-color: rgba(255, 255, 255, 0.1);
}

.category-filter-item {
  display: flex;
  align-items: center;
  padding: 4px 10px;
  border-radius: 15px;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid #ddd;
  background-color: white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  height: 28px;
  position: relative;
  overflow: visible; /* Changed from hidden to visible to prevent text clipping */
}

.dark-mode .category-filter-item {
  border-color: #444;
  background-color: #2a2a3c;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

/* Remove upward movement on hover */
.category-filter-item:hover {
  box-shadow: inset 0 0 0 1px var(--hover-glow-color, rgba(255, 255, 255, 0.5));
}

.dark-mode .category-filter-item:hover {
  box-shadow: inset 0 0 0 1px var(--hover-glow-color, rgba(255, 255, 255, 0.5));
}

.category-filter-item.active {
  color: white;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
}

.dark-mode .category-filter-item.active {
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.4);
}

.category-filter-color {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-right: 6px;
  box-shadow: 0 0 3px rgba(0, 0, 0, 0.1);
}

.dark-mode .category-filter-color {
  box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);
}

.category-filter-name {
  margin-right: 3px;
  white-space: nowrap;
  overflow: visible;
  display: inline-block;
  text-overflow: unset; /* Ensure no ellipsis is applied */
  max-width: none; /* Remove any max-width constraints */
}

/* Style for the custom All button */
.custom-all-filter-item {
  margin-right: 8px;
}

.category-filter-badge {
  font-size: 0.65rem;
  background-color: rgba(255, 255, 255, 0.2);
  padding: 1px 5px;
  border-radius: 8px;
  margin-left: 5px;
  font-weight: 500;
}

.category-filter-divider {
  width: 90%;
  height: 1px;
  background-color: rgba(0, 0, 0, 0.1);
  margin: 5px auto;
}

.dark-mode .category-filter-divider {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Department category styling */
.department-category {
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  padding: 5px 12px;
  font-size: 0.7rem;
  transition: all 0.2s ease;
  position: relative;
  z-index: 1;
  height: auto;
  min-height: 30px;
  border-radius: 15px;
  overflow: visible; /* Changed from hidden to visible to show full text */
  white-space: nowrap;
  display: flex;
  align-items: center;
  max-width: none; /* Remove any max-width constraints */
  width: auto; /* Allow width to adjust to content */
}

.dark-mode .department-category {
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* All filter item styling */
.all-filter-item {
  padding: 5px 12px;
  font-size: 0.75rem;
  height: auto;
  min-height: 26px;
  margin-right: 5px;
  margin-top: 2px; /* Move down slightly to align with other bubbles */
  border-radius: 15px;
  overflow: hidden;
  position: relative;
  white-space: nowrap;
  display: flex;
  align-items: center;
  min-width: fit-content;
}

/* Internal glow effect for all category items - contained within the bubble */
.category-filter-item:hover::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--hover-glow-color, rgba(255, 255, 255, 0.15));
  opacity: 0;
  border-radius: 15px; /* Match the border-radius of the bubble */
  pointer-events: none;
  animation: pulseGlow 2s infinite;
  width: 100%; /* Ensure full width coverage */
  height: 100%; /* Ensure full height coverage */
  box-sizing: border-box; /* Include border in size calculation */
  overflow: hidden; /* Ensure the glow stays within the bubble */
  clip-path: inset(0 0 0 0 round 15px); /* Clip to bubble shape */
}

/* Wave animation for hover effect - contained within the bubble */
.category-filter-item:hover::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent 0%,
    var(--hover-glow-color, rgba(255, 255, 255, 0.3)) 50%,
    transparent 100%
  );
  opacity: 0;
  pointer-events: none;
  animation: waveGlow 3s infinite;
  border-radius: 15px; /* Match the border-radius of the bubble */
  box-sizing: border-box; /* Include border in size calculation */
  overflow: hidden; /* Ensure the wave stays within the bubble */
  clip-path: inset(0 0 0 0 round 15px); /* Clip to bubble shape */
}

/* Hover state */
.category-filter-item:hover {
  z-index: 2;
  border-color: var(--hover-glow-color, rgba(255, 255, 255, 0.5));
  transform: none; /* Prevent any size changes on hover */
  box-shadow: none; /* Prevent any shadow changes that might affect perceived size */
}

/* Dark mode hover effects */
.dark-mode .category-filter-item:hover::before {
  background-color: var(--hover-glow-color, rgba(255, 255, 255, 0.2));
}

.dark-mode .category-filter-item:hover::after {
  background: linear-gradient(
    90deg,
    transparent 0%,
    var(--hover-glow-color, rgba(255, 255, 255, 0.4)) 50%,
    transparent 100%
  );
}

.department-category .category-filter-badge {
  font-size: 0.6rem;
  padding: 1px 3px;
  margin-left: 3px;
  border-radius: 6px;
}

/* Department categories scroll container */
.department-categories-scroll-container,
.user-categories-scroll-container {
  position: relative;
  display: flex;
  align-items: center;
  max-width: 100%;
  overflow: hidden;
  padding: 0 15px; /* Add padding for the scroll buttons */
  margin: 3px 0;
}

/* Only show scroll buttons when container is scrollable and hovered */
.department-categories-scroll-container.scrollable:hover .scroll-left-btn,
.department-categories-scroll-container.scrollable:hover .scroll-right-btn {
  opacity: 1;
}

.user-categories-scroll-container.scrollable:hover .scroll-left-btn,
.user-categories-scroll-container.scrollable:hover .scroll-right-btn {
  opacity: 1;
}

/* Hide scroll buttons when container is not scrollable */
.department-categories-scroll-container:not(.scrollable) .scroll-left-btn,
.department-categories-scroll-container:not(.scrollable) .scroll-right-btn,
.user-categories-scroll-container:not(.scrollable) .scroll-left-btn,
.user-categories-scroll-container:not(.scrollable) .scroll-right-btn {
  display: none;
}

.department-categories-scroll-content,
.user-categories-scroll-content {
  display: flex;
  gap: 6px;
  padding: 0 3px;
  overflow-x: auto;
  scrollbar-width: none; /* Hide scrollbar for Firefox */
  -ms-overflow-style: none; /* Hide scrollbar for IE and Edge */
  scroll-behavior: smooth;
  width: 100%;
}

.department-categories-scroll-content::-webkit-scrollbar,
.user-categories-scroll-content::-webkit-scrollbar {
  display: none; /* Hide scrollbar for Chrome, Safari, and Opera */
}

/* Scroll buttons styling */
.scroll-left-btn,
.scroll-right-btn {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.95);
  border: 1px solid #ddd;
  cursor: pointer;
  z-index: 5;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
  transition: opacity 0.15s ease;
  opacity: 0; /* Completely hidden by default */
}

.dark-mode .scroll-left-btn,
.dark-mode .scroll-right-btn {
  background-color: rgba(42, 42, 60, 0.95);
  border-color: #444;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.25);
}

.scroll-left-btn {
  left: 0;
}

.scroll-right-btn {
  right: 0;
}

.scroll-left-btn:hover,
.scroll-right-btn:hover {
  background-color: rgba(74, 118, 168, 0.1);
  transform: scale(1.1);
}

.dark-mode .scroll-left-btn:hover,
.dark-mode .scroll-right-btn:hover {
  background-color: rgba(74, 118, 168, 0.2);
}

.scroll-left-btn i,
.scroll-right-btn i {
  font-size: 12px;
  color: #4a76a8;
}

.dark-mode .scroll-left-btn i,
.dark-mode .scroll-right-btn i {
  color: #5fa8ff;
}

/* Contact category dropdown styles */
.contact-category-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  margin-left: 5px;
}

.contact-category-dropdown {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  cursor: pointer;
  position: relative;
  margin-left: 8px;
  transition: all 0.2s ease;
  background-color: rgba(74, 144, 226, 0.08);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  align-self: center;
}

.dark-mode .contact-category-dropdown {
  background-color: rgba(74, 144, 226, 0.12);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.contact-category-dropdown i {
  font-size: 11px;
  color: #4a90e2;
  filter: drop-shadow(0 0 3px rgba(74, 144, 226, 0.3));
  transition: all 0.3s ease;
}

.dark-mode .contact-category-dropdown i {
  color: #5fa8ff;
  filter: drop-shadow(0 0 4px rgba(95, 168, 255, 0.4));
}

.contact-category-dropdown:hover {
  background-color: rgba(74, 144, 226, 0.1);
}

.dark-mode .contact-category-dropdown:hover {
  background-color: rgba(74, 144, 226, 0.2);
}

.contact-category-dropdown:hover i {
  color: #4a90e2;
  filter: drop-shadow(0 0 3px rgba(74, 144, 226, 0.4));
}

.dark-mode .contact-category-dropdown:hover i {
  color: #5fa8ff;
  filter: drop-shadow(0 0 4px rgba(95, 168, 255, 0.5));
}

/* Add animation for dropdown */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive styles */
@media (max-width: 576px) {
  .category-manager {
    width: 90%;
    max-width: none;
    max-height: 90vh;
    padding: 15px;
  }

  .color-picker {
    gap: 8px;
  }

  .color-option {
    width: 25px;
    height: 25px;
  }

  .form-actions {
    flex-direction: column;
  }

  .btn-primary,
  .btn-secondary {
    width: 100%;
  }

  .contact-category-dropdown {
    width: 24px;
    height: 24px;
  }
}

/* Pulse glow animation for internal glow */
@keyframes pulseGlow {
  0% {
    opacity: 0.2;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 0.2;
  }
}

/* Wave animation for ripple effect - contained within the bubble */
@keyframes waveGlow {
  0% {
    opacity: 0;
    transform: translateX(-50%);
    border-radius: 15px; /* Keep border radius throughout animation */
    clip-path: inset(0 0 0 0 round 15px); /* Clip to bubble shape */
  }
  50% {
    opacity: 0.5;
    transform: translateX(0%);
    border-radius: 15px; /* Keep border radius throughout animation */
    clip-path: inset(0 0 0 0 round 15px); /* Clip to bubble shape */
  }
  100% {
    opacity: 0;
    transform: translateX(50%);
    border-radius: 15px; /* Keep border radius throughout animation */
    clip-path: inset(0 0 0 0 round 15px); /* Clip to bubble shape */
  }
}
