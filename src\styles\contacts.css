.contacts-page-container {
  padding: 1px;
  max-width: 1600px; /* Increased from 1200px */
  margin: 0 auto;
  height: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.contacts-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0px;
}

.contacts-header h2 {
  font-size: 1.8rem;
  margin: 0;
  color: #333;
}

.dark-mode .contacts-header h2 {
  color: #f1f1f1;
}

.contacts-actions {
  display: flex;
  align-items: center;
  gap: 15px;
}

.search-bar {
  display: flex;
  align-items: center;
  border-radius: 20px;
  padding: 0 15px;
  background-color: #f5f5f5;
  border: 1px solid #e0e0e0;
  height: 40px;
  width: 250px;
}

.dark-mode .search-bar {
  background-color: #2c2c2c;
  border-color: #444;
}

.search-bar i {
  color: #777;
  margin-right: 10px;
}

.dark-mode .search-bar i {
  color: #aaa;
}

.search-bar input {
  border: none;
  background: transparent;
  outline: none;
  width: 100%;
  color: #333;
  font-size: 0.95rem;
}

.dark-mode .search-bar input {
  color: #f0f0f0;
}

.search-bar input::placeholder {
  color: #999;
}

.dark-mode .search-bar input::placeholder {
  color: #777;
}

.department-filter {
  position: relative;
}

.department-select {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background-color: #f5f5f5;
  border: 1px solid #e0e0e0;
  border-radius: 20px;
  padding: 0 35px 0 15px;
  height: 40px;
  width: 200px;
  color: #333;
  font-size: 0.95rem;
  cursor: pointer;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23777' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 15px center;
  background-size: 15px;
  transition: all 0.3s ease;
}

.department-select:focus {
  outline: none;
  border-color: #4481eb;
  box-shadow: 0 0 0 2px rgba(68, 129, 235, 0.2);
}

.dark-mode .department-select {
  background-color: #2c2c2c;
  border-color: #444;
  color: white;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23aaa' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
}

.department-select option {
  font-weight: 500;
  background-color: white;
  color: #333;
}

.dark-mode .department-select option {
  background-color: #333;
  color: white;
}

.department-note {
  color: #666;
  font-size: 0.9rem;
  margin: 0;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 5px;
  border-left: 3px solid #4481eb;
}

.dark-mode .department-note {
  color: #ccc;
  background-color: #333;
  border-left-color: #4481eb;
}

.add-contact-btn {
  background-image: linear-gradient(to right, #4481eb, #04befe);
  color: white;
  border: none;
  border-radius: 20px;
  padding: 0 22px;
  height: 42px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 10px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(68, 129, 235, 0.2);
}

.add-contact-btn:hover {
  background-image: linear-gradient(to right, #3b74d9, #04a9e2);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(68, 129, 235, 0.3);
}

.contacts-list-container {
  background-color: #fff;
  border-radius: 20px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  transition: all 0.3s ease;
}

.contacts-list-container:hover {
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.08);
}

.dark-mode .contacts-list-container {
  background-color: #2d2d2d;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.dark-mode .contacts-list-container:hover {
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.25);
}

.contacts-table {
  width: 100%;
  border-collapse: collapse;
}

.contacts-table th {
  padding: 20px 25px; /* Increased from 15px 20px */
  text-align: left;
  font-weight: 600;
  color: #555;
  background-color: #f9f9f9;
  border-bottom: 1px solid #eee;
  font-size: 1rem; /* Added for better readability */
}

.dark-mode .contacts-table th {
  color: #ddd;
  background-color: #333;
  border-bottom-color: #444;
}

.contacts-table td {
  padding: 18px 25px; /* Increased from 15px 20px */
  border-bottom: 1px solid #eee;
  color: #333;
  font-size: 0.95rem; /* Added for better readability */
}

.dark-mode .contacts-table td {
  border-bottom-color: #444;
  color: #f0f0f0;
}

.contacts-table tr:last-child td {
  border-bottom: none;
}

.contacts-table tr:hover td {
  background-color: #f5f9ff;
}

.dark-mode .contacts-table tr:hover td {
  background-color: #3a3a3a;
}

.contact-name-cell {
  display: flex;
  align-items: center;
  gap: 12px;
}

.contact-avatar {
  width: 48px; /* Increased from 42px */
  height: 48px; /* Increased from 42px */
  border-radius: 16px; /* Increased from 14px */
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  color: white;
  font-size: 1.1rem; /* Increased from 1rem */
  /* background-image removed to allow custom colors */
  box-shadow: 0 4px 10px rgba(68, 129, 235, 0.3),
    0 0 12px rgba(68, 129, 235, 0.2);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  animation: contact-avatar-glow 4s ease-in-out infinite;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.contact-avatar::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: rotate(45deg);
  animation: contact-wave-shine 6s linear infinite,
    contact-wave-pulse 8s ease-in-out infinite;
  z-index: 1;
}

.contact-avatar::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    145deg,
    rgba(255, 255, 255, 0.3) 0%,
    rgba(255, 255, 255, 0) 60%
  );
  border-radius: 16px;
  z-index: 0;
  animation: contact-wave-ripple 10s ease-in-out infinite;
}

.contact-avatar:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 15px rgba(68, 129, 235, 0.4),
    0 0 15px rgba(68, 129, 235, 0.3);
  animation: contact-avatar-glow-hover 2s ease-in-out infinite;
}

@keyframes contact-wave-shine {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
}

@keyframes contact-avatar-glow {
  0% {
    box-shadow: 0 4px 10px rgba(68, 129, 235, 0.3),
      0 0 12px rgba(68, 129, 235, 0.2);
  }
  50% {
    box-shadow: 0 6px 15px rgba(68, 129, 235, 0.5),
      0 0 18px rgba(68, 129, 235, 0.4);
  }
  100% {
    box-shadow: 0 4px 10px rgba(68, 129, 235, 0.3),
      0 0 12px rgba(68, 129, 235, 0.2);
  }
}

@keyframes contact-avatar-glow-hover {
  0% {
    box-shadow: 0 6px 15px rgba(68, 129, 235, 0.4),
      0 0 15px rgba(68, 129, 235, 0.3);
  }
  50% {
    box-shadow: 0 8px 20px rgba(68, 129, 235, 0.6),
      0 0 20px rgba(68, 129, 235, 0.5);
  }
  100% {
    box-shadow: 0 6px 15px rgba(68, 129, 235, 0.4),
      0 0 15px rgba(68, 129, 235, 0.3);
  }
}

@keyframes contact-wave-pulse {
  0% {
    opacity: 0.7;
    width: 200%;
    height: 200%;
  }
  50% {
    opacity: 0.9;
    width: 220%;
    height: 220%;
  }
  100% {
    opacity: 0.7;
    width: 200%;
    height: 200%;
  }
}

@keyframes contact-wave-ripple {
  0% {
    transform: scale(1);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.9;
  }
  100% {
    transform: scale(1);
    opacity: 0.7;
  }
}

.status-indicator {
  display: inline-block;
  padding: 5px 10px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.status-indicator.active {
  background-color: #e7f7e8;
  color: #2e7d32;
}

.dark-mode .status-indicator.active {
  background-color: #1e3320;
  color: #4caf50;
}

.status-indicator.inactive {
  background-color: #f7f7f7;
  color: #757575;
}

.dark-mode .status-indicator.inactive {
  background-color: #2c2c2c;
  color: #aaa;
}

/* Department badge styles */
.department-badge {
  display: inline-block;
  padding: 4px 10px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  color: white;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.department-badge:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Wave animation for department badge */
.department-badge::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.4) 50%,
    transparent 100%
  );
  opacity: 0;
  pointer-events: none;
  animation: contactDepartmentWaveGlow 5s infinite;
  border-radius: 12px;
  clip-path: inset(0 0 0 0 round 12px);
  z-index: 1;
}

/* Internal glow effect */
.department-badge::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.2);
  opacity: 0;
  border-radius: 12px;
  pointer-events: none;
  animation: contactDepartmentPulseGlow 4s infinite;
  clip-path: inset(0 0 0 0 round 12px);
  z-index: 0;
}

/* Wave animation for department glow */
@keyframes contactDepartmentWaveGlow {
  0% {
    opacity: 0;
    transform: translateX(-50%);
  }
  50% {
    opacity: 0.7;
    transform: translateX(0%);
  }
  100% {
    opacity: 0;
    transform: translateX(50%);
  }
}

/* Pulse animation for department glow */
@keyframes contactDepartmentPulseGlow {
  0% {
    opacity: 0;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 0;
  }
}

/* Department-specific colors */
.department-badge.human-resources {
  background-color: #ff6b6b;
}

.department-badge.marketing {
  background-color: #4ecdc4;
}

.department-badge.finance {
  background-color: #45b7d1;
}

.department-badge.it {
  background-color: #7367f0;
}

.department-badge.operations {
  background-color: #ff9f43;
}

.department-badge.sales {
  background-color: #28c76f;
}

.department-badge.customer-service {
  background-color: #ea5455;
}

.department-badge.research-development {
  background-color: #9f44d3;
}

.department-badge.development {
  background-color: #5a8dee;
}

.department-badge.legal {
  background-color: #a66a2c;
}

.department-badge.executive {
  background-color: #475f7b;
}

/* Dark mode adjustments */
.dark-mode .department-badge {
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.contact-actions {
  display: flex;
  gap: 12px; /* Increased from 8px */
}

.action-btn {
  width: 42px; /* Increased from 38px */
  height: 42px; /* Increased from 38px */
  border-radius: 14px; /* Increased from 13px */
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: white;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  font-size: 1.1rem; /* Increased from 1rem */
}

.action-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.action-btn.message-btn {
  background-color: #3a86ff;
}

.action-btn.message-btn:hover {
  background-color: #2a75f0;
}

.action-btn.edit-btn {
  background-color: #ffc857;
}

.action-btn.edit-btn:hover {
  background-color: #f0b846;
}

.action-btn.delete-btn {
  background-color: #ef476f;
}

.action-btn.delete-btn:hover {
  background-color: #e03a62;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.add-contact-modal {
  background-color: white;
  border-radius: 10px;
  width: 450px;
  max-width: 90%;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  overflow: hidden;
}

.dark-mode .add-contact-modal {
  background-color: #2d2d2d;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.4);
}

.modal-header {
  padding: 20px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dark-mode .modal-header {
  border-bottom-color: #444;
}

.modal-header h3 {
  margin: 0;
  color: #333;
  font-size: 1.3rem;
}

.dark-mode .modal-header h3 {
  color: #f1f1f1;
}

.close-modal-btn {
  background: none;
  border: none;
  font-size: 1.2rem;
  color: #777;
  cursor: pointer;
}

.dark-mode .close-modal-btn {
  color: #aaa;
}

.modal-body {
  padding: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #555;
}

.dark-mode .form-group label {
  color: #ddd;
}

.form-group input {
  width: 100%;
  padding: 10px 15px;
  border-radius: 5px;
  border: 1px solid #ddd;
  font-size: 1rem;
  color: #333;
  transition: border-color 0.2s;
}

.dark-mode .form-group input {
  background-color: #333;
  border-color: #555;
  color: #f0f0f0;
}

.form-group input:focus {
  outline: none;
  border-color: #4a6cf7;
}

.modal-footer {
  padding: 15px 20px;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.dark-mode .modal-footer {
  border-top-color: #444;
}

.cancel-btn,
.add-btn {
  padding: 8px 20px;
  border-radius: 5px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.cancel-btn {
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  color: #555;
}

.dark-mode .cancel-btn {
  background-color: #3a3a3a;
  border-color: #555;
  color: #ddd;
}

.cancel-btn:hover {
  background-color: #eee;
}

.dark-mode .cancel-btn:hover {
  background-color: #444;
}

.add-btn {
  background-color: #4a6cf7;
  border: none;
  color: white;
}

.add-btn:hover {
  background-color: #3959d9;
}

.add-btn:disabled {
  background-color: #a0aee9;
  cursor: not-allowed;
}

.error-message {
  background-color: #ffebee;
  color: #d32f2f;
  padding: 10px;
  border-radius: 5px;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.dark-mode .error-message {
  background-color: #3e2929;
  color: #f48fb1;
}

.success-message {
  background-color: #e8f5e9;
  color: #2e7d32;
  padding: 10px;
  border-radius: 5px;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.dark-mode .success-message {
  background-color: #1e3320;
  color: #81c784;
}

.confirm-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.65);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.confirm-dialog {
  background-color: white;
  border-radius: 24px;
  width: 340px;
  padding: 30px;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
  text-align: center;
  animation: dialog-enter 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateY(0) scale(1);
}

@keyframes dialog-enter {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.dark-mode .confirm-dialog {
  background-color: #2d2d3a;
  color: #e0e0e0;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
}

.confirm-dialog h4 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 24px;
  color: #333;
}

.dark-mode .confirm-dialog h4 {
  color: #e0e0e0;
}

.confirm-dialog p {
  margin-bottom: 30px;
  color: #555;
  line-height: 1.6;
  font-size: 16px;
}

.dark-mode .confirm-dialog p {
  color: #b0b0b0;
}

.confirm-actions {
  display: flex;
  justify-content: center;
  gap: 15px;
}

.btn-cancel,
.btn-confirm {
  padding: 12px 24px;
  border-radius: 12px;
  border: none;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 15px;
}

.btn-cancel {
  background-color: #f0f0f0;
  color: #555;
}

.dark-mode .btn-cancel {
  background-color: #3a3a48;
  color: #e0e0e0;
}

.btn-cancel:hover {
  background-color: #e0e0e0;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.dark-mode .btn-cancel:hover {
  background-color: #44445a;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.btn-confirm {
  background-color: #ff4757;
  color: white;
}

.btn-confirm:hover {
  background-color: #e04252;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(255, 71, 87, 0.3);
}

/* Responsive adjustments */
@media (max-width: 1200px) {
  .contacts-list-container {
    overflow-x: auto;
    white-space: nowrap;
  }

  .contacts-table {
    min-width: 800px;
  }
}

@media (max-width: 768px) {
  .contacts-header {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }

  .contacts-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .search-bar {
    width: 100%;
  }

  .add-contact-btn {
    justify-content: center;
  }

  .contacts-table th:nth-child(3),
  .contacts-table td:nth-child(3) {
    display: none;
  }
}

@media (max-width: 576px) {
  .contacts-table th:nth-child(2),
  .contacts-table td:nth-child(2) {
    display: none;
  }
}
