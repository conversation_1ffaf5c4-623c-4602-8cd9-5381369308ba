/* Dashboard Layout */
.dashboard-container {
  display: flex;
  height: 100vh;
  background-color: #f8f9fa;
  position: relative;
  overflow: hidden;
  transition: background-color 0.3s ease;
}

.dark-mode.dashboard-container {
  background-color: #1a1a1a;
}

.dashboard-content {
  flex: 1;
  margin-left: 80px;
  position: relative;
  overflow-y: auto;
  height: 100vh;
  transition: margin-left 0.3s ease;
  width: calc(100% - 80px);
}

/* Page transition animations */
.page-content {
  animation: pageEntranceAnimation 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94)
    forwards;
  opacity: 0;
  transform: translateY(20px);
  height: 100%;
  display: flex;
  flex-direction: column;
}

@keyframes pageEntranceAnimation {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Slide-in-left animation for cards and content */
.slide-in-left {
  animation: slideInLeft 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  opacity: 0;
  transform: translateX(-30px);
}

@keyframes slideInLeft {
  0% {
    opacity: 0;
    transform: translateX(-30px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Slide-in-right animation */
.slide-in-right {
  animation: slideInRight 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  opacity: 0;
  transform: translateX(30px);
}

@keyframes slideInRight {
  0% {
    opacity: 0;
    transform: translateX(30px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Slide-up animation */
.slide-up {
  animation: slideUp 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  opacity: 0;
  transform: translateY(30px);
}

@keyframes slideUp {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Fade-in animation */
.fade-in {
  animation: fadeIn 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  opacity: 0;
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

/* Scale-in animation */
.scale-in {
  animation: scaleIn 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
  opacity: 0;
  transform: scale(0.95);
}

@keyframes scaleIn {
  0% {
    opacity: 0;
    transform: scale(0.95);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Staggered animation delays */
.stagger-1 {
  animation-delay: 0.1s;
}
.stagger-2 {
  animation-delay: 0.2s;
}
.stagger-3 {
  animation-delay: 0.3s;
}
.stagger-4 {
  animation-delay: 0.4s;
}
.stagger-5 {
  animation-delay: 0.5s;
}
.stagger-6 {
  animation-delay: 0.6s;
}

/* Animation performance optimizations */
.slide-in-left,
.slide-in-right,
.slide-up,
.fade-in,
.scale-in,
.page-content {
  will-change: transform, opacity;
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  .slide-in-left,
  .slide-in-right,
  .slide-up,
  .fade-in,
  .scale-in,
  .page-content {
    animation: none !important;
    opacity: 1 !important;
    transform: none !important;
  }
}

.sidebar.expanded ~ .dashboard-content {
  margin-left: 220px;
  width: calc(100% - 220px);
}

/* Dark Mode */
.dashboard-container.dark-mode {
  background-color: #1e1e2d;
  color: #e9e9e9;
}

/* Text Sizes */
.text-small {
  font-size: 0.85rem;
}

.text-medium {
  font-size: 1rem;
}

.text-large {
  font-size: 1.2rem;
}

/* Responsive */
@media (max-width: 1200px) {
  .dashboard-content {
    margin-left: 220px;
    padding: 20px;
  }
}

@media (max-width: 992px) {
  .dashboard-content {
    margin-left: 200px;
    padding: 15px;
  }

  .dashboard-header h1 {
    font-size: 1.6rem;
  }

  .dashboard-card {
    padding: 15px;
  }
}

@media (max-width: 768px) {
  .dashboard-container {
    flex-direction: row;
  }

  .dashboard-content {
    margin-left: 60px;
    margin-top: 0;
    width: calc(100% - 60px);
    padding: 15px 12px;
    transition: margin-left 0.3s ease;
  }

  .sidebar.collapsed ~ .dashboard-content {
    margin-left: 0;
    width: 100%;
  }

  .dashboard-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
    margin-bottom: 15px;
  }

  .dashboard-header h1 {
    font-size: 1.4rem;
    margin-bottom: 5px;
  }

  .dashboard-actions {
    width: 100%;
    justify-content: space-between;
  }

  .dashboard-card {
    margin-bottom: 15px;
  }
}

@media (max-width: 576px) {
  .dashboard-container {
    flex-direction: column;
    height: 100vh;
    overflow: hidden;
  }

  .dashboard-content {
    margin-left: 0;
    width: 100%;
    height: calc(
      100vh - 110px
    ); /* Adjust for top header (50px) and bottom navigation (60px) */
    padding: 10px;
    margin-top: 1px; /* Top header height */
    margin-bottom: 1px; /* Bottom navigation height */
    overflow-y: auto;
  }

  /* Sidebar styles are handled in sidebar.css */

  .sidebar.expanded ~ .dashboard-content {
    margin-left: 0;
    width: 100%;
  }

  .dashboard-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
    margin-bottom: 15px;
  }

  .dashboard-header h1 {
    font-size: 1.3rem;
    margin-bottom: 5px;
  }

  .dashboard-actions {
    width: 100%;
    flex-wrap: wrap;
    gap: 8px;
  }

  .dashboard-card {
    padding: 12px;
    border-radius: 10px;
    margin-bottom: 15px;
  }

  .dashboard-card h2 {
    font-size: 1.1rem;
    margin-bottom: 10px;
  }

  .dashboard-card-content {
    font-size: 0.9rem;
  }

  .dashboard-actions button {
    padding: 6px 10px;
    font-size: 0.85rem;
  }

  /* Modal adjustments for small screens */
  .modal-container {
    width: 95%;
    max-height: 80vh;
  }

  .modal-header {
    padding: 12px 15px;
  }

  .modal-body {
    padding: 15px;
  }

  .modal-footer {
    padding: 12px 15px;
  }
}

/* Common Card Styles */
.card {
  background-color: #fff;
  border-radius: 16px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.06);
  margin-bottom: 24px;
  overflow: hidden;
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94),
    box-shadow 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.card:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 25px rgba(0, 0, 0, 0.08);
}

/* Enhanced card animations */
.card.slide-in-left,
.card.slide-in-right,
.card.slide-up,
.card.fade-in,
.card.scale-in {
  animation-fill-mode: both;
}

.dashboard-container.dark-mode .card {
  background-color: #2d2d3a;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

.dashboard-container.dark-mode .card:hover {
  box-shadow: 0 12px 25px rgba(0, 0, 0, 0.25);
}

.card-header {
  padding: 15px 20px;
  border-bottom: 1px solid #eaeaea;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dashboard-container.dark-mode .card-header {
  border-bottom: 1px solid #3a3a48;
}

.card-body {
  padding: 20px;
}

/* Success message */
.success-message {
  background-color: #d4edda;
  color: #155724;
  padding: 10px 15px;
  margin-bottom: 20px;
  border-radius: 4px;
  display: flex;
  align-items: center;
}

.success-message i {
  margin-right: 10px;
  font-size: 1.2rem;
}

.dashboard-container.dark-mode .success-message {
  background-color: #1e4635;
  color: #8fd1a8;
}

/* Error message */
.error-message {
  background-color: #f8d7da;
  color: #721c24;
  padding: 10px 15px;
  margin-bottom: 20px;
  border-radius: 4px;
  display: flex;
  align-items: center;
}

.error-message i {
  margin-right: 10px;
  font-size: 1.2rem;
}

.dashboard-container.dark-mode .error-message {
  background-color: #442a2d;
  color: #f8d7da;
}

/* Loading */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  width: 100%;
  background-color: #f8f9fa;
  font-size: 1.5rem;
  color: #6c757d;
}

.dashboard-container.dark-mode .loading-container {
  background-color: #1e1e2d;
  color: #e9e9e9;
}

/* Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #eaeaea;
}

.dashboard-container.dark-mode .modal-header {
  border-bottom: 1px solid #3a3a48;
}

.modal-body {
  padding: 20px;
}

.modal-footer {
  padding: 15px 20px;
  border-top: 1px solid #eaeaea;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.dashboard-container.dark-mode .modal-footer {
  border-top: 1px solid #3a3a48;
}

.close-modal-btn {
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  color: #6c757d;
}

.dashboard-container.dark-mode .close-modal-btn {
  color: #e9e9e9;
}

.close-modal-btn:hover {
  color: #343a40;
}

.dashboard-container.dark-mode .close-modal-btn:hover {
  color: #ffffff;
}

/* Form elements */
.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  background-color: #fff;
}

.dashboard-container.dark-mode .form-group input,
.dashboard-container.dark-mode .form-group select,
.dashboard-container.dark-mode .form-group textarea {
  background-color: #2d2d3a;
  border-color: #3a3a48;
  color: #e9e9e9;
}

.input-error {
  border-color: #dc3545 !important;
}

/* Buttons */
button {
  cursor: pointer;
  transition: all 0.3s ease;
}

button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}
