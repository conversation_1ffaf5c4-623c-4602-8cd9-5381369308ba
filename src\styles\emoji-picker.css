/* Emoji Picker Styles */
.emoji-picker-container {
  position: absolute;
  bottom: 80px;
  right: 20px;
  z-index: 100;
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
  border-radius: 10px;
  overflow: hidden;
  width: auto !important;
  height: auto !important;
  max-width: 350px !important;
  max-height: 435px !important;
  background: transparent !important;
}

.dark-mode .emoji-picker-container {
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.4);
}

.emoji-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #f5f5f5;
  color: #6c757d;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.dark-mode .emoji-button {
  background-color: #35354a;
  color: #a7a7b3;
}

.emoji-button:hover {
  background-color: #eaeaea;
  color: #4a6cf7;
}

.dark-mode .emoji-button:hover {
  background-color: #44445a;
  color: #7a9fff;
}

/* Fix for emoji-picker-react library overlay issues */
.EmojiPickerReact {
  width: auto !important;
  height: auto !important;
  max-width: 350px !important;
  max-height: 435px !important;
}

/* Fix for any overlay elements in the emoji picker */
.emoji-picker-container .EmojiPickerReact {
  position: static !important;
  box-shadow: none !important;
}

/* Ensure the emoji picker doesn't create a full-width overlay */
.emoji-picker-container .EmojiPickerReact::before,
.emoji-picker-container .EmojiPickerReact::after {
  content: none !important;
  display: none !important;
}

/* Fix search icon position in emoji picker */
.emoji-picker-container .epr-search-container .epr-icn-search {
  position: absolute !important;
  left: 10px !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
}

.emoji-picker-container .epr-search-container input {
  padding-left: 35px !important;
}

/* Ensure no full-width background elements */
.EmojiPickerReact > div {
  width: auto !important;
}

.EmojiPickerReact .epr-body {
  width: auto !important;
}

/* Fix for any overlay elements */
.EmojiPickerReact::before,
.EmojiPickerReact::after {
  display: none !important;
}

.dark-mode .EmojiPickerReact {
  background-color: #2d2d3a !important;
}

/* Responsive styles */
@media (max-width: 768px) {
  .emoji-picker-container {
    bottom: 70px;
    right: 10px;
    width: 280px !important;
  }

  .emoji-button {
    width: 36px;
    height: 36px;
  }
}
