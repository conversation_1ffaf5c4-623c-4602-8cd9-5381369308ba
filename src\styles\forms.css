form {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  padding: 0 5rem;
  grid-column: 1 / 2;
  grid-row: 1 / 2;
  transition: 1.2s ease-in-out;
}

.admin-option {
  margin-bottom: 15px;
}

.admin-checkbox {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.admin-checkbox input {
  margin-right: 10px;
}

.checkbox-text {
  font-size: 14px;
  color: #555;
}

.admin-role-selection {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 15px;
  padding: 10px;
  background-color: rgba(248, 249, 250, 0.7);
  border-radius: 10px;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.admin-role-option {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 5px;
}

.admin-role-option input {
  margin-right: 10px;
}

.radio-text {
  font-size: 14px;
  color: #555;
}

.sign-in-form {
  z-index: 10;
}

.sign-up-form {
  z-index: 2;
  opacity: 0;
}

.sign-up-mode .sign-in-form {
  z-index: 2;
  opacity: 0;
}

.sign-up-mode .sign-up-form {
  z-index: 2;
  opacity: 1;
}

.title {
  font-size: 2.2rem;
  color: #444;
  margin-bottom: 10px;
}

.input-field {
  max-width: 380px;
  width: 100%;
  height: 55px;
  background-color: #f0f0f0;
  margin: 12px 0;
  border-radius: 55px;
  display: grid;
  grid-template-columns: 15% 85%;
  padding: 0 0.4rem;
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
}

.input-field:focus-within {
  box-shadow: 0 4px 10px rgba(68, 129, 235, 0.15);
  transform: translateY(-2px);
}

.input-field i {
  text-align: center;
  line-height: 55px;
  color: #acacac;
  font-size: 1.1rem;
}

.input-field input {
  background: none;
  outline: none;
  border: none;
  line-height: 1;
  font-weight: 600;
  font-size: 1.1rem;
  color: #333;
}

.input-field input::placeholder {
  color: #aaa;
  font-weight: 500;
}

.input-field select {
  background: none;
  outline: none;
  border: none;
  line-height: 1;
  font-weight: 600;
  font-size: 1.1rem;
  color: #333;
  width: 100%;
  height: 100%;
  padding-right: 15px;
  padding-left: 5px;
  cursor: pointer;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%23acacac' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 10px center;
  background-size: 15px;
}

/* Style for the select when it has the default value */
.input-field select:invalid,
.input-field select option:first-child {
  color: #333 !important;
}

.input-field select option[value=""] {
  color: #333 !important;
  font-weight: 600;
}

/* Force the placeholder color */
select[name="department"] {
  color: #333 !important;
}

.dark-mode .input-field select {
  color: white;
}

.input-field select option {
  font-weight: 500;
  background-color: white !important;
  color: black !important;
  padding: 8px 25px;
  text-indent: 10px;
}

.dark-mode .input-field select option {
  background-color: white !important;
  color: black !important;
}

.btn {
  width: 150px;
  height: 49px;
  border: none;
  outline: none;
  border-radius: 49px;
  cursor: pointer;
  background-image: linear-gradient(to right, #4481eb, #04befe);
  color: #fff;
  text-transform: uppercase;
  font-weight: 600;
  margin: 12px 0;
  transition: all 0.5s ease;
  box-shadow: 0 4px 15px rgba(68, 129, 235, 0.25);
}

.btn:hover {
  background-image: linear-gradient(to right, #3b74d9, #04a9e2);
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(68, 129, 235, 0.35);
}

.btn.transparent {
  margin: 0;
  background: none;
  border: 2px solid #fff;
  width: 130px;
  height: 41px;
  font-weight: 600;
  font-size: 0.8rem;
}

.social-text {
  padding: 0.7rem 0;
  font-size: 1rem;
}

.social-media {
  display: flex;
  justify-content: center;
}

.social-icon {
  height: 46px;
  width: 46px;
  border: 1px solid #333;
  margin: 0 0.45rem;
  display: flex;
  justify-content: center;
  align-items: center;
  text-decoration: none;
  color: #333;
  font-size: 1.1rem;
  border-radius: 50%;
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.social-icon:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(68, 129, 235, 0.2);
}

.social-icon:hover {
  color: #4481eb;
  border-color: #4481eb;
}
