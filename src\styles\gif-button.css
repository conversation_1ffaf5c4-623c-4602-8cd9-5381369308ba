.gif-button {
  width: 36px;
  height: 36px;
  background-color: transparent;
  border: none;
  border-radius: 6px;
  color: #4a89dc;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica,
    Arial, sans-serif;
  margin: 0 4px;
  font-weight: 600;
}

.gif-button span {
  font-size: 13px;
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

.gif-button:hover {
  background-color: rgba(74, 137, 220, 0.1);
  color: #3a79cc;
  transform: translateY(-2px);
}

/* Dark mode styles */
.dark-mode .gif-button {
  background-color: transparent;
  color: #5a99ec;
}

.dark-mode .gif-button:hover {
  background-color: rgba(90, 153, 236, 0.15);
  color: #7ab0ff;
}

/* Add this to both messaging.css and groupchat.css */
.input-container .gif-button {
  display: flex;
  align-items: center;
  justify-content: center;
}
