.group-chat-container {
  display: flex;
  height: 100%;
  flex: 1;
  background-color: #fff;
  border-radius: 20px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: all 0.3s ease;
}

.dark-mode .group-chat-container {
  background-color: #2d2d3a;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

/* Group List */
.groups-sidebar {
  width: 350px;
  border-right: 1px solid #eaeaea;
  display: flex;
  flex-direction: column;
  background-color: #f8f8f8;
  overflow: hidden;
}

.dark-mode .groups-sidebar {
  border-right: 1px solid #3a3a48;
  background-color: #2d2d3a;
}

.groups-header {
  padding: 20px;
  border-bottom: none;
  background-color: #fff;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.03);
  position: relative;
  z-index: 2;
}

.dark-mode .groups-header {
  border-bottom: none;
  background-color: #222230;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.groups-header h2 {
  margin: 0 0 15px 0;
  font-size: 1.3rem;
  color: #333;
  font-weight: 600;
}

.dark-mode .groups-header h2 {
  color: #e9e9e9;
}

.create-group-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  background-image: linear-gradient(to right, #4481eb, #04befe);
  color: white;
  border: none;
  border-radius: 12px;
  padding: 14px;
  width: 100%;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(68, 129, 235, 0.25);
}

.dark-mode .create-group-btn {
  background-image: linear-gradient(to right, #4481eb, #04befe);
  box-shadow: 0 4px 15px rgba(68, 129, 235, 0.15);
}

.create-group-btn:hover {
  background-image: linear-gradient(to right, #3b74d9, #04a9e2);
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(68, 129, 235, 0.35);
}

.dark-mode .create-group-btn:hover {
  background-image: linear-gradient(to right, #3b74d9, #04a9e2);
  box-shadow: 0 6px 20px rgba(68, 129, 235, 0.25);
}

.groups-search {
  padding: 15px 50px;
  background-color: #fff;
  border-bottom: 1px solid #eaeaea;
  position: relative;
  z-index: 1;
}

.dark-mode .groups-search {
  background-color: #222230;
  border-bottom: 1px solid #3a3a48;
}

.search-bar {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 20px;
  padding: 10px 15px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.search-bar:focus-within {
  box-shadow: 0 2px 5px rgba(74, 108, 247, 0.15);
}

.dark-mode .search-bar {
  background-color: #35354a;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.dark-mode .search-bar:focus-within {
  box-shadow: 0 2px 5px rgba(122, 159, 255, 0.15);
}

.search-bar i {
  color: #6c757d;
  margin-right: 10px;
}

.dark-mode .search-bar i {
  color: #a7a7b3;
}

.search-bar input {
  border: none;
  background: transparent;
  width: 100%;
  outline: none;
  color: #333;
}

.dark-mode .search-bar input {
  color: #e9e9e9;
}

.search-bar input::placeholder {
  color: #adb5bd;
}

.dark-mode .search-bar input::placeholder {
  color: #6c7293;
}

.groups-list {
  flex: 1;
  overflow-y: auto;
  padding: 10px 0;
}

.group-item {
  display: flex;
  align-items: center;
  padding: 14px 15px;
  margin: 6px 10px;
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid transparent;
  background-color: #fff;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  position: relative;
}

/* Priority message styling for group items */
.group-item.priority-high {
  border-color: #ff9800;
  box-shadow: 0 4px 12px rgba(255, 152, 0, 0.2);
  animation: glow-high 2s infinite alternate;
  background-color: rgba(255, 152, 0, 0.05);
}

.group-item.priority-urgent {
  border-color: #f44336;
  box-shadow: 0 4px 12px rgba(244, 67, 54, 0.4);
  animation: glow-urgent 1.2s infinite alternate;
  background-color: rgba(244, 67, 54, 0.1);
  position: relative;
  z-index: 2;
  border-width: 2px;
  position: relative;
  overflow: visible;
}

.group-item.priority-urgent::before {
  content: "";
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  border: 2px solid rgba(244, 67, 54, 0.6);
  border-radius: 18px;
  animation: pulse-border 1.5s infinite;
  pointer-events: none;
  z-index: -1;
}

@keyframes pulse-border {
  0% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(1.03);
  }
  100% {
    opacity: 0.6;
    transform: scale(1);
  }
}

/* Dark mode styles for priority messages */
.dark-mode .group-item.priority-high {
  border-color: #ff9800;
  box-shadow: 0 4px 12px rgba(255, 152, 0, 0.3);
  animation: glow-high-dark 2s infinite alternate;
  background-color: rgba(255, 152, 0, 0.1);
}

.dark-mode .group-item.priority-urgent {
  border-color: #f44336;
  box-shadow: 0 4px 12px rgba(244, 67, 54, 0.5);
  animation: glow-urgent-dark 1.2s infinite alternate;
  background-color: rgba(244, 67, 54, 0.18);
  position: relative;
  z-index: 2;
  border-width: 2px;
  position: relative;
  overflow: visible;
}

.dark-mode .group-item.priority-urgent::before {
  content: "";
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  border: 2px solid rgba(244, 67, 54, 0.7);
  border-radius: 18px;
  animation: pulse-border-dark 1.5s infinite;
  pointer-events: none;
  z-index: -1;
}

@keyframes pulse-border-dark {
  0% {
    opacity: 0.7;
    transform: scale(1);
  }
  50% {
    opacity: 0.4;
    transform: scale(1.03);
  }
  100% {
    opacity: 0.7;
    transform: scale(1);
  }
}

@keyframes glow-high {
  0% {
    box-shadow: 0 4px 12px rgba(255, 152, 0, 0.2);
    transform: translateY(0);
  }
  50% {
    box-shadow: 0 4px 25px rgba(255, 152, 0, 0.6);
    transform: translateY(-2px);
  }
  100% {
    box-shadow: 0 4px 12px rgba(255, 152, 0, 0.2);
    transform: translateY(0);
  }
}

@keyframes glow-urgent {
  0% {
    box-shadow: 0 4px 12px rgba(244, 67, 54, 0.4);
    transform: translateY(0);
    border-color: rgba(244, 67, 54, 0.7);
  }
  50% {
    box-shadow: 0 4px 30px rgba(244, 67, 54, 0.9);
    transform: translateY(-2px);
    border-color: rgba(244, 67, 54, 1);
    border-width: 2px;
  }
  100% {
    box-shadow: 0 4px 12px rgba(244, 67, 54, 0.4);
    transform: translateY(0);
    border-color: rgba(244, 67, 54, 0.7);
  }
}

@keyframes glow-high-dark {
  0% {
    box-shadow: 0 4px 12px rgba(255, 152, 0, 0.3);
    transform: translateY(0);
  }
  50% {
    box-shadow: 0 4px 25px rgba(255, 152, 0, 0.7);
    transform: translateY(-2px);
  }
  100% {
    box-shadow: 0 4px 12px rgba(255, 152, 0, 0.3);
    transform: translateY(0);
  }
}

@keyframes glow-urgent-dark {
  0% {
    box-shadow: 0 4px 12px rgba(244, 67, 54, 0.5);
    transform: translateY(0);
    border-color: rgba(244, 67, 54, 0.8);
  }
  50% {
    box-shadow: 0 4px 30px rgba(244, 67, 54, 1);
    transform: translateY(-2px);
    border-color: rgba(244, 67, 54, 1);
    border-width: 2px;
  }
  100% {
    box-shadow: 0 4px 12px rgba(244, 67, 54, 0.5);
    transform: translateY(0);
    border-color: rgba(244, 67, 54, 0.8);
  }
}

.dark-mode .group-item {
  background-color: #2a2a3a;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
}

.group-item:hover {
  background-color: #f7f9ff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border-color: rgba(74, 108, 247, 0.1);
}

.dark-mode .group-item:hover {
  background-color: #303045;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: rgba(122, 159, 255, 0.2);
}

.group-item.active {
  background-color: #eef2ff;
  border-color: rgba(74, 108, 247, 0.2);
  box-shadow: 0 4px 15px rgba(74, 108, 247, 0.15);
}

.dark-mode .group-item.active {
  background-color: #2a2a45;
  border-color: rgba(122, 159, 255, 0.3);
  box-shadow: 0 4px 15px rgba(74, 108, 247, 0.2);
}

.group-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: #4a6cf7;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  margin-right: 15px;
  font-size: 1rem;
  flex-shrink: 0;
  box-shadow: 0 3px 6px rgba(74, 108, 247, 0.3),
    0 0 12px rgba(74, 108, 247, 0.2);
  position: relative;
  transition: all 0.3s ease;
  overflow: hidden;
  animation: group-avatar-glow 4s ease-in-out infinite;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.group-avatar::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: rotate(45deg);
  animation: group-wave-shine 6s linear infinite,
    group-wave-pulse 8s ease-in-out infinite;
  z-index: 1;
}

.group-avatar::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    145deg,
    rgba(255, 255, 255, 0.3) 0%,
    rgba(255, 255, 255, 0) 60%
  );
  border-radius: 50%;
  z-index: 0;
  animation: group-wave-ripple 10s ease-in-out infinite;
}

.group-item:hover .group-avatar {
  transform: scale(1.05);
  animation: group-avatar-glow-hover 2s ease-in-out infinite;
}

.dark-mode .group-avatar {
  box-shadow: 0 3px 6px rgba(74, 108, 247, 0.15);
}

@keyframes group-wave-shine {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
}

@keyframes group-avatar-glow {
  0% {
    box-shadow: 0 3px 6px rgba(74, 108, 247, 0.3),
      0 0 12px rgba(74, 108, 247, 0.2);
  }
  50% {
    box-shadow: 0 5px 12px rgba(74, 108, 247, 0.5),
      0 0 18px rgba(74, 108, 247, 0.4);
  }
  100% {
    box-shadow: 0 3px 6px rgba(74, 108, 247, 0.3),
      0 0 12px rgba(74, 108, 247, 0.2);
  }
}

@keyframes group-avatar-glow-hover {
  0% {
    box-shadow: 0 5px 12px rgba(74, 108, 247, 0.4),
      0 0 15px rgba(74, 108, 247, 0.3);
  }
  50% {
    box-shadow: 0 8px 18px rgba(74, 108, 247, 0.6),
      0 0 20px rgba(74, 108, 247, 0.5);
  }
  100% {
    box-shadow: 0 5px 12px rgba(74, 108, 247, 0.4),
      0 0 15px rgba(74, 108, 247, 0.3);
  }
}

@keyframes group-wave-pulse {
  0% {
    opacity: 0.7;
    width: 200%;
    height: 200%;
  }
  50% {
    opacity: 0.9;
    width: 220%;
    height: 220%;
  }
  100% {
    opacity: 0.7;
    width: 200%;
    height: 200%;
  }
}

@keyframes group-wave-ripple {
  0% {
    transform: scale(1);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.9;
  }
  100% {
    transform: scale(1);
    opacity: 0.7;
  }
}

.group-info {
  flex: 1;
  min-width: 0;
  margin-right: 8px;
}

.group-name {
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  transition: color 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: normal;
}

.unread-badge {
  background-color: #ff4757;
  color: white;
  border-radius: 50%;
  min-width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  margin-left: 8px;
  padding: 0 4px;
  font-weight: bold;
  box-shadow: 0 2px 4px rgba(255, 71, 87, 0.3);
}

.dark-mode .group-name {
  color: #e9e9e9;
}

.group-item:hover .group-name {
  color: #4a6cf7;
}

.dark-mode .group-item:hover .group-name {
  color: #7a9fff;
}

.group-last-message {
  font-size: 0.85rem;
  color: #6c757d;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 180px;
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
}

.dark-mode .group-last-message {
  color: #a7a7b3;
}

/* Urgency prefix styles */
.urgency-prefix {
  font-weight: bold;
  margin-right: 2px;
  white-space: nowrap;
}

.urgency-prefix.urgent {
  color: #f44336;
}

.urgency-prefix.high {
  color: #ff9800;
}

.dark-mode .urgency-prefix.urgent {
  color: #ff8a80;
}

.dark-mode .urgency-prefix.high {
  color: #ffcc80;
}

.group-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 5px;
}

.group-time {
  font-size: 0.75rem;
  color: #6c757d;
}

.dark-mode .group-time {
  color: #a7a7b3;
}

.unread-count {
  background-color: #4a6cf7;
  color: white;
  min-width: 20px;
  height: 20px;
  border-radius: 10px;
  font-size: 0.7rem;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 5px;
  box-shadow: 0 2px 4px rgba(74, 108, 247, 0.15);
}

.dark-mode .unread-count {
  background-color: #5a77ff;
  box-shadow: 0 2px 4px rgba(74, 108, 247, 0.1);
}

/* Chat Area */
.chat-area {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.chat-header {
  padding: 15px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #eaeaea;
  margin-top: 0;
  padding-top: 15px;
}

.dark-mode .chat-header {
  border-bottom: 1px solid #3a3a48;
}

.chat-group-info {
  display: flex;
  align-items: center;
}

.chat-group-info .group-avatar {
  margin-right: 15px;
  border-radius: 50%;
}

.chat-group-details {
  display: flex;
  flex-direction: column;
}

.chat-group-name {
  font-weight: 500;
  color: #333;
  margin-bottom: 3px;
}

.dark-mode .chat-group-name {
  color: #e9e9e9;
}

.chat-group-members {
  font-size: 0.85rem;
  color: #6c757d;
}

.dark-mode .chat-group-members {
  color: #a7a7b3;
}

.chat-actions {
  display: flex;
  gap: 10px;
}

.chat-action-button {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border: none;
  background-color: #f5f5f5;
  color: #6c757d;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.dark-mode .chat-action-button {
  background-color: #35354a;
  color: #a7a7b3;
}

.chat-action-button:hover {
  background-color: #e9ecef;
  color: #4a6cf7;
}

.dark-mode .chat-action-button:hover {
  background-color: #3a3a4a;
  color: #5a77ff;
}

.chat-messages {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.message-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.message-group.incoming {
  align-self: flex-start;
  background-color: #fff;
  color: #333;
  border-bottom-left-radius: 5px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.message-group.outgoing {
  align-self: flex-end;
  background-color: #4a6cf7;
  color: #fff;
  border-bottom-right-radius: 5px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.message-sender {
  font-size: 0.85rem;
  margin-left: 12px;
  color: #6c757d;
}

.dark-mode .message-sender {
  color: #a7a7b3;
}

.message {
  max-width: 70%;
  padding: 12px 15px;
  border-radius: 12px;
  position: relative;
  cursor: pointer;
}

/* Message urgency styling */
.message.urgency-high {
  border-left: 4px solid #ff9800;
  background-color: rgba(255, 152, 0, 0.05);
}

.message.urgency-urgent {
  border-left: 4px solid #f44336;
  background-color: rgba(244, 67, 54, 0.05);
  animation: message-urgent-pulse 2s infinite;
}

.dark-mode .message.urgency-high {
  background-color: rgba(255, 152, 0, 0.1);
}

.dark-mode .message.urgency-urgent {
  background-color: rgba(244, 67, 54, 0.1);
  animation: message-urgent-pulse-dark 2s infinite;
}

@keyframes message-urgent-pulse {
  0% {
    border-color: #f44336;
    box-shadow: 0 0 5px rgba(244, 67, 54, 0.5);
  }
  50% {
    border-color: #ff6659;
    box-shadow: 0 0 10px rgba(244, 67, 54, 0.7);
  }
  100% {
    border-color: #f44336;
    box-shadow: 0 0 5px rgba(244, 67, 54, 0.5);
  }
}

@keyframes message-urgent-pulse-dark {
  0% {
    border-color: #f44336;
    box-shadow: 0 0 5px rgba(244, 67, 54, 0.6);
  }
  50% {
    border-color: #ff6659;
    box-shadow: 0 0 10px rgba(244, 67, 54, 0.8);
  }
  100% {
    border-color: #f44336;
    box-shadow: 0 0 5px rgba(244, 67, 54, 0.6);
  }
}

.message .sender-name {
  font-size: 0.8em;
  color: #666;
  margin-bottom: 2px;
  position: absolute;
  top: -20px;
  left: 0;
}

.message.outgoing .sender-name {
  display: none;
}

.message.incoming {
  background-color: #f5f5f5;
  color: #333;
  border-top-left-radius: 2px;
  margin-top: 24px; /* Add space for sender name */
}

.dark-mode .message.incoming {
  background-color: #35354a;
  color: #e9e9e9;
}

.message.outgoing {
  background-color: #4a6cf7;
  color: #fff;
  border-top-right-radius: 2px;
}

.message.deleted .message-text {
  background-color: #f1f1f1;
  color: #999;
  font-style: italic;
}

.dark-mode .message.deleted .message-text {
  background-color: #3a3a48;
  color: #888;
}

/* Message options menu */
.message-options {
  position: absolute;
  top: -40px;
  right: 0;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  display: flex;
  overflow: hidden;
  z-index: 10;
}

.message-options-vertical {
  flex-direction: column;
  top: -80px;
  right: 10px;
  width: auto;
}

.message-options-trigger {
  position: absolute;
  top: 5px;
  right: 5px;
  color: rgba(0, 0, 0, 0.5);
  cursor: pointer;
  padding: 5px;
  font-size: 0.8rem;
  border-radius: 50%;
  display: none;
}

.message:hover .message-options-trigger {
  display: block;
}

.dark-mode .message-options-trigger {
  color: rgba(255, 255, 255, 0.5);
}

.dark-mode .message-options {
  background-color: #2d2d3a;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.message-option-btn {
  border: none;
  background: none;
  padding: 8px 12px;
  cursor: pointer;
  color: #333;
  font-size: 0.85rem;
  display: flex;
  align-items: center;
  gap: 5px;
  transition: background-color 0.2s;
  flex-direction: row;
}

.dark-mode .message-option-btn {
  color: #e9e9e9;
}

.message-option-btn:hover {
  background-color: #f5f5f5;
}

.dark-mode .message-option-btn:hover {
  background-color: #3a3a48;
}

.message-option-btn.delete {
  color: #ff4757;
}

.dark-mode .message-option-btn.delete {
  color: #ff6b81;
}

/* Edit message styles */
.edit-message-container {
  width: 100%;
  margin-bottom: 5px;
}

.edit-message-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 0.9rem;
  margin-bottom: 5px;
}

.dark-mode .edit-message-input {
  background-color: #35354a;
  border-color: #444;
  color: #e9e9e9;
}

.edit-message-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.edit-save-btn,
.edit-cancel-btn {
  border: none;
  padding: 5px 10px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 0.8rem;
  display: flex;
  align-items: center;
  gap: 5px;
}

.edit-save-btn {
  background-color: #4a6cf7;
  color: white;
}

.dark-mode .edit-save-btn {
  background-color: #4a6cf7;
}

.edit-cancel-btn {
  background-color: #f1f1f1;
  color: #666;
}

.dark-mode .edit-cancel-btn {
  background-color: #3a3a48;
  color: #ccc;
}

.dark-mode .message.outgoing {
  background-color: #5a77ff;
}

.message-text {
  line-height: 1.4;
  margin-bottom: 5px;
}

.message-time {
  font-size: 0.7rem;
  opacity: 0.7;
  text-align: right;
  display: flex;
  align-items: center;
  gap: 5px;
}

.edited-label {
  font-size: 0.65rem;
  color: #999;
  font-style: italic;
}

/* Quick Reply Buttons */
.quick-reply-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 8px;
  margin-bottom: 5px;
  max-width: 100%;
}

.quick-reply-btn {
  background-color: #f5f7ff;
  border: 1px solid #e0e4f6;
  border-radius: 18px;
  padding: 6px 12px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #4a6cf7;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  white-space: nowrap;
}

.quick-reply-btn:hover {
  background-color: #eef2ff;
  transform: translateY(-2px);
  box-shadow: 0 3px 6px rgba(74, 108, 247, 0.1);
}

.quick-reply-btn:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(74, 108, 247, 0.1);
}

.dark-mode .quick-reply-btn {
  background-color: #2a2a45;
  border: 1px solid #3a3a55;
  color: #7a9fff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.dark-mode .quick-reply-btn:hover {
  background-color: #303050;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
}

.system-message {
  align-self: center;
  background-color: #f8f9fa;
  color: #6c757d;
  padding: 8px 12px;
  border-radius: 15px;
  font-size: 0.85rem;
  max-width: 80%;
  text-align: center;
  margin: 10px 0;
}

.dark-mode .system-message {
  background-color: #35354a;
  color: #a7a7b3;
}

.date-divider {
  align-self: center;
  font-size: 0.8rem;
  color: #6c757d;
  background-color: #f8f9fa;
  padding: 5px 15px;
  border-radius: 15px;
  margin: 10px 0;
}

.dark-mode .date-divider {
  background-color: #35354a;
  color: #a7a7b3;
}

.chat-input-area {
  padding: 15px 20px;
  border-top: 1px solid #eaeaea;
  display: flex;
  align-items: center;
  gap: 10px;
}

.dark-mode .chat-input-area {
  border-top: 1px solid #3a3a48;
}

.chat-input-area input {
  flex: 1;
  padding: 10px 15px;
  border-radius: 20px;
  outline: none;
}

.dark-mode .chat-input-area input {
  background-color: #35354a;
  border-color: #3a3a48;
  color: #e9e9e9;
}

.attachment-button,
.send-button {
  width: 38px;
  height: 38px;
  border-radius: 50%;
  border: none;
  background-color: #f5f5f5;
  color: #6c757d;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.dark-mode .attachment-button,
.dark-mode .send-button {
  background-color: #35354a;
  color: #a7a7b3;
}

.attachment-button:hover,
.send-button:hover {
  background-color: #e9ecef;
  color: #4a6cf7;
}

.dark-mode .attachment-button:hover,
.dark-mode .send-button:hover {
  background-color: #3a3a4a;
  color: #5a77ff;
}

.send-button {
  background-color: #4a6cf7;
  color: #fff;
}

.dark-mode .send-button {
  background-color: #5a77ff;
  color: #fff;
}

.send-button:hover {
  background-color: #385de0;
  color: #fff;
}

.dark-mode .send-button:hover {
  background-color: #4a66e0;
}

/* Group Details Sidebar */
.group-details-sidebar {
  width: 280px;
  border-left: 1px solid #eaeaea;
  display: flex;
  flex-direction: column;
}

.dark-mode .group-details-sidebar {
  border-left: 1px solid #3a3a48;
}

.group-details-header {
  padding: 20px;
  border-bottom: 1px solid #eaeaea;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dark-mode .group-details-header {
  border-bottom: 1px solid #3a3a48;
}

.group-details-header h3 {
  margin: 0;
  font-size: 1.1rem;
  color: #333;
}

.dark-mode .group-details-header h3 {
  color: #e9e9e9;
}

.close-details-button {
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  font-size: 1.2rem;
}

.dark-mode .close-details-button {
  color: #a7a7b3;
}

.group-details-content {
  padding: 20px;
  flex: 1;
  overflow-y: auto;
}

.group-description {
  margin-bottom: 25px;
}

.group-description h4 {
  font-size: 0.9rem;
  color: #6c757d;
  margin: 0 0 10px 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.dark-mode .group-description h4 {
  color: #a7a7b3;
}

.group-description p {
  color: #333;
  font-size: 0.95rem;
  line-height: 1.5;
  margin: 0;
}

.dark-mode .group-description p {
  color: #e9e9e9;
}

.group-members-section h4 {
  font-size: 0.9rem;
  color: #6c757d;
  margin: 0 0 15px 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dark-mode .group-members-section h4 {
  color: #a7a7b3;
}

.members-count {
  background-color: #e9ecef;
  color: #495057;
  font-size: 0.75rem;
  padding: 2px 8px;
  border-radius: 10px;
}

.dark-mode .members-count {
  background-color: #3a3a4a;
  color: #a7a7b3;
}

.member-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 15px 0;
  max-height: 300px;
  overflow-y: auto;
}

.member-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-radius: 8px;
  transition: background-color 0.2s ease;
}

.member-item:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.dark-mode .member-item:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

.member-avatar {
  width: 40px;
  height: 40px;
  border-radius: 18px;
  background-color: #4a6cf7;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 16px;
  margin-right: 12px;
  position: relative;
  overflow: hidden;
  animation: member-avatar-glow 4s ease-in-out infinite;
}

.member-avatar::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.1) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: rotate(45deg);
  animation: member-wave-shine 6s linear infinite;
  z-index: 1;
}

.member-avatar::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    145deg,
    rgba(255, 255, 255, 0.2) 0%,
    rgba(255, 255, 255, 0) 60%
  );
  border-radius: 18px;
  z-index: 0;
}

@keyframes member-wave-shine {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
}

@keyframes member-avatar-glow {
  0% {
    box-shadow: 0 3px 8px rgba(74, 108, 247, 0.2),
      0 0 12px rgba(74, 108, 247, 0.1);
  }
  50% {
    box-shadow: 0 5px 15px rgba(74, 108, 247, 0.4),
      0 0 18px rgba(74, 108, 247, 0.3);
  }
  100% {
    box-shadow: 0 3px 8px rgba(74, 108, 247, 0.2),
      0 0 12px rgba(74, 108, 247, 0.1);
  }
}

@keyframes member-wave-pulse {
  0% {
    opacity: 0.7;
    width: 200%;
    height: 200%;
  }
  50% {
    opacity: 0.9;
    width: 220%;
    height: 220%;
  }
  100% {
    opacity: 0.7;
    width: 200%;
    height: 200%;
  }
}

@keyframes member-wave-ripple {
  0% {
    transform: scale(1);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.9;
  }
  100% {
    transform: scale(1);
    opacity: 0.7;
  }
}

@keyframes member-wave-shine {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
}

.member-details {
  flex: 1;
  margin: 0 12px;
}

.member-name {
  font-weight: 500;
  color: #333;
  margin-bottom: 3px;
}

.dark-mode .member-name {
  color: #e9e9e9;
}

.member-role {
  font-size: 0.8rem;
  color: #6c757d;
}

.dark-mode .member-role {
  color: #a7a7b3;
}

.member-status {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.85rem;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
}

.status-online .status-indicator {
  background-color: #2ecc71;
  box-shadow: 0 0 0 2px rgba(46, 204, 113, 0.2);
}

.status-offline .status-indicator {
  background-color: #95a5a6;
  box-shadow: 0 0 0 2px rgba(149, 165, 166, 0.2);
}

.status-text {
  color: #666;
}

.dark-mode .status-text {
  color: #a7a7b3;
}

/* Add status indicator to group list items */
.group-item .member-status {
  position: absolute;
  right: 8px;
  top: 8px;
  padding: 0;
  background: none;
}

.group-item .status-indicator {
  width: 6px;
  height: 6px;
}

.group-actions {
  padding: 20px;
  border-top: 1px solid #eaeaea;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

/* Search functionality */
.search-conversation-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  position: relative;
  margin-bottom: 15px;
}

/* Organize the group actions buttons */
.group-actions {
  display: flex;
  flex-direction: column;
  gap: 15px;
  padding-top: 15px;
}

/* Create sections in the group actions */
.group-actions-section {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 10px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.dark-mode .group-actions-section {
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.group-actions-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.chat-search-bar {
  width: 90%;
  margin: 5px auto;
  background-color: #f5f5f5;
  border-radius: 12px;
  padding: 10px 15px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  max-height: 0;
  opacity: 0;
  transition: all 0.3s ease;
}

.dark-mode .chat-search-bar {
  background-color: #35354a;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.chat-search-bar.active {
  max-height: 50px;
  opacity: 1;
  margin: 10px auto;
}

.chat-search-bar i {
  color: #777;
  margin-right: 10px;
  font-size: 0.9rem;
}

.dark-mode .chat-search-bar i {
  color: #aaa;
}

.chat-search-bar input {
  flex: 1;
  border: none;
  background: transparent;
  outline: none;
  font-size: 0.9rem;
  color: #333;
}

.dark-mode .chat-search-bar input {
  color: #e0e0e0;
}

.chat-search-bar input::placeholder {
  color: #999;
}

.dark-mode .chat-search-bar input::placeholder {
  color: #777;
}

/* Search navigation */
.search-navigation {
  width: 90%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 0 auto;
  padding: 5px 0;
  opacity: 0;
  max-height: 0;
  overflow: hidden;
  transition: all 0.3s ease;
}

#search-navigation.active {
  opacity: 1;
  max-height: 40px;
  margin: 5px auto;
}

.search-count {
  font-size: 0.85rem;
  color: #666;
}

.dark-mode .search-count {
  color: #aaa;
}

.search-navigation-buttons {
  display: flex;
  gap: 5px;
}

.search-navigation-buttons button {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  border: none;
  background-color: #f0f0f0;
  color: #555;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.dark-mode .search-navigation-buttons button {
  background-color: #2a2a3a;
  color: #aaa;
}

.search-navigation-buttons button:hover:not(:disabled) {
  background-color: #e0e0e0;
  color: #4a6cf7;
}

.dark-mode .search-navigation-buttons button:hover:not(:disabled) {
  background-color: #3a3a4a;
  color: #7a9fff;
}

.search-navigation-buttons button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Search highlight */
.search-highlight {
  position: relative;
  animation: search-pulse 2s infinite;
  box-shadow: 0 0 10px rgba(74, 108, 247, 0.7);
  border: 2px solid rgba(74, 108, 247, 0.4);
  z-index: 1;
}

.current-highlight {
  box-shadow: 0 0 15px rgba(74, 108, 247, 1) !important;
  border: 2px solid rgba(74, 108, 247, 0.8) !important;
  animation: current-search-pulse 2s infinite !important;
  z-index: 2;
}

.dark-mode .search-highlight {
  box-shadow: 0 0 10px rgba(122, 159, 255, 0.7);
  border: 2px solid rgba(122, 159, 255, 0.4);
}

.dark-mode .current-highlight {
  box-shadow: 0 0 15px rgba(122, 159, 255, 1) !important;
  border: 2px solid rgba(122, 159, 255, 0.8) !important;
}

@keyframes search-pulse {
  0% {
    box-shadow: 0 0 10px rgba(74, 108, 247, 0.7);
  }
  50% {
    box-shadow: 0 0 15px rgba(74, 108, 247, 0.8);
  }
  100% {
    box-shadow: 0 0 10px rgba(74, 108, 247, 0.7);
  }
}

@keyframes current-search-pulse {
  0% {
    box-shadow: 0 0 15px rgba(74, 108, 247, 1);
  }
  50% {
    box-shadow: 0 0 20px rgba(74, 108, 247, 1);
  }
  100% {
    box-shadow: 0 0 15px rgba(74, 108, 247, 1);
  }
}

/* Emoji Picker Styles */
.emoji-picker-container {
  position: absolute;
  bottom: 60px;
  left: 10px;
  z-index: 100;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
  width: auto !important;
  height: auto !important;
  max-width: 350px !important;
  max-height: 435px !important;
  background: transparent !important;
}

/* Ensure emoji picker doesn't create a full-width overlay */
.emoji-picker-container > div {
  width: auto !important;
  background: transparent !important;
}

.emoji-picker-container > div > div {
  width: auto !important;
}

.dark-mode .emoji-picker-container {
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
}

/* Override emoji-picker-react styles for dark mode */
.dark-mode .emoji-picker-container .epr-body {
  background-color: #2d2d3a !important;
}

.dark-mode .emoji-picker-container .epr-search-container {
  background-color: #2d2d3a !important;
  border-color: #444 !important;
}

.dark-mode .emoji-picker-container .epr-search-container input {
  background-color: #35354a !important;
  border-color: #444 !important;
  color: #e0e0e0 !important;
}

.dark-mode .emoji-picker-container .epr-category-nav {
  background-color: #222230 !important;
  border-color: #444 !important;
}

.dark-mode .emoji-picker-container .epr-emoji-category-label {
  background-color: #2d2d3a !important;
  color: #a7a7b3 !important;
}

.dark-mode .emoji-picker-container .epr-preview {
  background-color: #222230 !important;
  border-color: #444 !important;
}

.dark-mode .emoji-picker-container .epr-preview .epr-preview-emoji-label {
  color: #e0e0e0 !important;
}

.dark-mode .emoji-picker-container .epr-emoji:hover {
  background-color: #35354a !important;
}

/* Additional dark mode overrides for emoji picker */
.dark-mode .emoji-picker-container .epr-header {
  background-color: #222230 !important;
  border-color: #444 !important;
}

.dark-mode .emoji-picker-container .epr-header .epr-header-overlay {
  background-color: #222230 !important;
}

.dark-mode .emoji-picker-container .epr-search {
  background-color: #35354a !important;
  border-color: #444 !important;
  color: #e0e0e0 !important;
}

.dark-mode .emoji-picker-container .epr-emoji-category {
  background-color: #2d2d3a !important;
}

.dark-mode .emoji-picker-container .epr-body .epr-emoji-list {
  background-color: #2d2d3a !important;
}

.dark-mode .emoji-picker-container .epr-body::-webkit-scrollbar-track {
  background-color: #2d2d3a !important;
}

.dark-mode .emoji-picker-container .epr-body::-webkit-scrollbar-thumb {
  background-color: #444 !important;
}

.dark-mode .emoji-picker-container .epr-skin-tones {
  background-color: #222230 !important;
}

.dark-mode .emoji-picker-container .epr-frequently-used {
  background-color: #2d2d3a !important;
  color: #a7a7b3 !important;
}

.dark-mode .emoji-picker-container .epr-search-container .epr-icn-search {
  color: #a7a7b3 !important;
}

.dark-mode .emoji-picker-container .epr-category-nav .epr-active-category {
  background-color: #35354a !important;
  border-color: #444 !important;
}

.dark-mode .emoji-picker-container .epr-category-nav button {
  color: #a7a7b3 !important;
}

.dark-mode .emoji-picker-container .epr-category-nav button:hover {
  background-color: #35354a !important;
}

.dark-mode .emoji-picker-container .epr-search-container .epr-icn-search svg {
  fill: #a7a7b3 !important;
}

/* Additional fixes for any remaining white elements */
.dark-mode .emoji-picker-container * {
  border-color: #444 !important;
}

/* Specific dark background for emoji picker elements */
.dark-mode .emoji-picker-container .epr-body,
.dark-mode .emoji-picker-container .epr-search-container,
.dark-mode .emoji-picker-container .epr-header,
.dark-mode .emoji-picker-container .epr-category-nav,
.dark-mode .emoji-picker-container .epr-emoji-category,
.dark-mode .emoji-picker-container .epr-preview {
  background-color: #2d2d3a !important;
  color: #e0e0e0 !important;
}

/* Fix search input styling in dark mode */
.dark-mode .emoji-picker-container .epr-search-container input {
  background-color: #35354a !important;
  color: #e0e0e0 !important;
  border-color: #444 !important;
}

.dark-mode .emoji-picker-container .epr-emoji-list > div {
  background-color: #2d2d3a !important;
}

.dark-mode .emoji-picker-container .epr-emoji-list > div > div {
  background-color: #2d2d3a !important;
}

.dark-mode .emoji-picker-container .epr-emoji-list > div > div > div {
  background-color: #2d2d3a !important;
}

.dark-mode .emoji-picker-container .epr-emoji-list > div > div > div > div {
  background-color: #2d2d3a !important;
}

.dark-mode
  .emoji-picker-container
  .epr-emoji-list
  > div
  > div
  > div
  > div
  > div {
  background-color: #2d2d3a !important;
}

.dark-mode
  .emoji-picker-container
  .epr-emoji-list
  > div
  > div
  > div
  > div
  > div
  > div {
  background-color: #2d2d3a !important;
}

.dark-mode
  .emoji-picker-container
  .epr-emoji-list
  > div
  > div
  > div
  > div
  > div
  > div
  > div {
  background-color: #2d2d3a !important;
}

.dark-mode
  .emoji-picker-container
  .epr-emoji-list
  > div
  > div
  > div
  > div
  > div
  > div
  > div
  > div {
  background-color: #2d2d3a !important;
}

.dark-mode
  .emoji-picker-container
  .epr-emoji-list
  > div
  > div
  > div
  > div
  > div
  > div
  > div
  > div
  > div {
  background-color: #2d2d3a !important;
}

.dark-mode
  .emoji-picker-container
  .epr-emoji-list
  > div
  > div
  > div
  > div
  > div
  > div
  > div
  > div
  > div
  > div {
  background-color: #2d2d3a !important;
}

.dark-mode .group-actions {
  border-top: 1px solid #3a3a48;
}

.group-action-button {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  border: none;
  border-radius: 4px;
  background-color: #f5f5f5;
  color: #333;
  cursor: pointer;
  transition: all 0.3s ease;
}

.dark-mode .group-action-button {
  background-color: #35354a;
  color: #e9e9e9;
}

.group-action-button:hover {
  background-color: #e9ecef;
}

.dark-mode .group-action-button:hover {
  background-color: #3a3a4a;
}

.group-action-button.danger {
  color: #dc3545;
}

.dark-mode .group-action-button.danger {
  color: #ea868f;
}

.group-action-button.danger:hover {
  background-color: rgba(220, 53, 69, 0.1);
}

.dark-mode .group-action-button.danger:hover {
  background-color: rgba(234, 134, 143, 0.1);
}

/* Delete messages button */
.delete-messages-button {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  border: none;
  border-radius: 4px;
  background-color: #f5f5f5;
  color: #dc3545;
  cursor: pointer;
  transition: all 0.3s ease;
}

.dark-mode .delete-messages-button {
  background-color: #35354a;
  color: #ea868f;
}

.delete-messages-button:hover {
  background-color: rgba(220, 53, 69, 0.1);
}

.dark-mode .delete-messages-button:hover {
  background-color: rgba(234, 134, 143, 0.1);
}

/* Create Group Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-container {
  background-color: #fff;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

.dark-mode .modal-container {
  background-color: #2d2d3a;
}

.modal-header {
  padding: 20px;
  border-bottom: 1px solid #eaeaea;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dark-mode .modal-header {
  border-bottom: 1px solid #3a3a48;
}

.modal-title {
  font-size: 1.3rem;
  font-weight: 500;
  color: #333;
  margin: 0;
}

.dark-mode .modal-title {
  color: #e9e9e9;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #6c757d;
}

.dark-mode .modal-close {
  color: #a7a7b3;
}

.modal-body {
  padding: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  color: #495057;
  font-weight: 500;
}

.dark-mode .form-group label {
  color: #e9e9e9;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 10px 15px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  outline: none;
}

.dark-mode .form-group input,
.dark-mode .form-group textarea {
  background-color: #35354a;
  border-color: #3a3a48;
  color: #e9e9e9;
}

.form-group textarea {
  min-height: 100px;
  resize: vertical;
}

.modal-footer {
  padding: 20px;
  border-top: 1px solid #eaeaea;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.dark-mode .modal-footer {
  border-top: 1px solid #3a3a48;
}

.modal-footer .cancel-button {
  background-color: #f0f0f0;
  color: #333;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  margin-right: 10px;
}

.modal-footer .confirm-button {
  background-color: #4a76a8;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
}

/* Dark mode styles for modal buttons */
.dark-mode .modal-footer .cancel-button {
  background-color: #3a3a3a;
  color: #e0e0e0;
  border: 1px solid #555;
}

.dark-mode .modal-footer .confirm-button {
  background-color: #4a76a8;
  color: white;
}

/* Bell animation for mute/unmute toggle */
.bell-animation {
  transition: transform 0.3s ease;
  margin-right: 8px;
}

.bell-animation:hover {
  transform: scale(1.2);
}

/* Add Member Section */
.add-members-section {
  margin-top: 20px;
}

.add-members-header {
  font-size: 0.9rem;
  color: #6c757d;
  margin: 0 0 15px 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.dark-mode .add-members-header {
  color: #a7a7b3;
}

.members-search {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 4px;
  padding: 8px 15px;
  margin-bottom: 15px;
}

.dark-mode .members-search {
  background-color: #35354a;
}

.members-search i {
  color: #6c757d;
  margin-right: 10px;
}

.dark-mode .members-search i {
  color: #a7a7b3;
}

.members-search input {
  border: none;
  background: transparent;
  width: 100%;
  outline: none;
}

.dark-mode .members-search input {
  color: #e9e9e9;
}

.selected-members {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 15px;
}

.selected-member {
  display: flex;
  align-items: center;
  background-color: #e9ecef;
  padding: 5px 10px 5px 15px;
  border-radius: 20px;
  font-size: 0.85rem;
  color: #495057;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.selected-member:hover {
  background-color: #e2e6ea;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.08);
}

.dark-mode .selected-member {
  background-color: #3a3a4a;
  color: #a7a7b3;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.dark-mode .selected-member:hover {
  background-color: #444454;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.25);
}

.remove-member {
  background: none;
  border: none;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8px;
  cursor: pointer;
  color: #6c757d;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.dark-mode .remove-member {
  color: #a7a7b3;
}

.remove-member:hover {
  color: #4a6cf7;
  transform: rotate(90deg);
  background-color: rgba(74, 108, 247, 0.1);
}

.dark-mode .remove-member:hover {
  color: #7a9fff;
  background-color: rgba(122, 159, 255, 0.1);
}

.suggested-members {
  display: flex;
  flex-direction: column;
  gap: 10px;
  max-height: 200px;
  overflow-y: auto;
}

.suggested-member {
  display: flex;
  align-items: center;
  padding: 10px;
  border-radius: 4px;
  cursor: pointer;
}

.suggested-member:hover {
  background-color: #f5f5f5;
}

.dark-mode .suggested-member:hover {
  background-color: #35354a;
}

.suggested-member-avatar {
  width: 36px;
  height: 36px;
  border-radius: 18px;
  background-color: #4a6cf7;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
  margin-right: 12px;
  position: relative;
  overflow: hidden;
  animation: member-avatar-glow 4s ease-in-out infinite;
}

.suggested-member-avatar::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.1) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: rotate(45deg);
  animation: member-wave-shine 6s linear infinite;
  z-index: 1;
}

.suggested-member-avatar::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    145deg,
    rgba(255, 255, 255, 0.2) 0%,
    rgba(255, 255, 255, 0) 60%
  );
  border-radius: 18px;
  z-index: 0;
}

.suggested-member-name {
  flex: 1;
  color: #333;
}

.dark-mode .suggested-member-name {
  color: #e9e9e9;
}

/* Available Contacts List */
.available-contacts {
  max-height: 300px;
  overflow-y: auto;
  margin-top: 1rem;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background-color: #fff;
}

.dark-mode .available-contacts {
  border: 1px solid #3a3a48;
  background-color: #2d2d3a;
}

.contact-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  cursor: pointer;
  transition: background-color 0.2s;
  border-bottom: 1px solid #f0f0f0;
  color: #333;
  background-color: #fff;
}

.dark-mode .contact-item {
  border-bottom: 1px solid #3a3a48;
  color: #e9e9e9;
  background-color: #2d2d3a;
}

.contact-item:last-child {
  border-bottom: none;
}

.contact-item:hover {
  background-color: #f8f9fa;
}

.dark-mode .contact-item:hover {
  background-color: #35354a;
}

.contact-item.selected {
  background-color: #e3f2fd;
}

.dark-mode .contact-item.selected {
  background-color: #2a2a45;
}

.contact-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 16px;
  margin-right: 1rem;
  position: relative;
  overflow: hidden;
  animation: member-avatar-glow 4s ease-in-out infinite;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.contact-avatar::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.1) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: rotate(45deg);
  animation: member-wave-shine 6s linear infinite;
  z-index: 1;
}

.contact-avatar::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    145deg,
    rgba(255, 255, 255, 0.2) 0%,
    rgba(255, 255, 255, 0) 60%
  );
  border-radius: 50%;
  z-index: 0;
}

.contact-name {
  flex: 1;
  font-size: 0.9rem;
  color: #333;
}

.dark-mode .contact-name {
  color: #e9e9e9;
}

.selected-indicator {
  color: #2196f3;
  margin-left: 0.5rem;
}

.dark-mode .selected-indicator {
  color: #5a77ff;
}

/* Responsive */
@media (max-width: 1200px) {
  .group-chat-container {
    height: calc(100vh - 20px);
  }

  .groups-sidebar {
    width: 300px;
  }

  .group-last-message {
    max-width: 150px;
  }
}

@media (max-width: 992px) {
  .group-details-sidebar {
    position: fixed;
    right: 0;
    top: 0;
    bottom: 0;
    width: 300px;
    z-index: 10;
    background-color: #fff;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
  }

  .dark-mode .group-details-sidebar {
    background-color: #2d2d3a;
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.2);
  }

  .group-details-sidebar.open {
    transform: translateX(0);
  }

  .groups-sidebar {
    width: 280px;
  }

  .chat-area {
    flex: 1;
  }

  .group-last-message {
    max-width: 120px;
  }

  .chat-input-container {
    padding: 12px;
  }

  .message-input {
    padding: 10px 12px;
  }
}

@media (max-width: 768px) {
  .group-chat-container {
    height: calc(100vh - 20px);
    overflow: hidden;
    position: relative;
  }

  .groups-sidebar {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 10;
    background-color: #f8f8f8;
    transition: transform 0.3s ease-in-out;
    transform: translateX(0);
  }

  .dark-mode .groups-sidebar {
    background-color: #2d2d3a;
  }

  .groups-sidebar.mobile-hidden {
    transform: translateX(-100%);
  }

  .chat-area {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 5;
    display: flex;
    flex-direction: column;
    background-color: #fff;
    transition: transform 0.3s ease-in-out;
    transform: translateX(100%);
    margin-top: 0;
    padding-top: 0;
  }

  .dark-mode .chat-area {
    background-color: #222230;
  }

  .chat-area.mobile-visible {
    transform: translateX(0);
    z-index: 15;
  }

  .chat-messages {
    flex: 1;
    overflow-y: auto;
  }

  .group-details-sidebar {
    width: 100%;
    height: 100vh;
    z-index: 20;
  }

  .group-item {
    padding: 10px 12px;
    margin: 4px 8px;
  }

  .group-avatar {
    width: 40px;
    height: 40px;
    margin-right: 10px;
    border-radius: 50%;
  }

  .group-tabs {
    padding: 8px 10px;
    display: flex;
    align-items: center;
  }

  .tab-btn {
    padding: 8px 12px;
    font-size: 0.9rem;
  }

  .message-input {
    font-size: 0.95rem;
  }

  .chat-input-actions {
    gap: 8px;
  }

  .back-button {
    background: transparent;
    border: none;
    color: #4a6cf7;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 5px 10px;
    margin-right: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
  }

  .dark-mode .back-button {
    color: #7a9fff;
  }

  .back-button:hover {
    transform: translateX(-3px);
  }
}

@media (max-width: 576px) {
  .group-chat-container {
    height: calc(100vh - 60px);
    margin-top: 60px;
    overflow: hidden;
    position: relative;
  }

  /* Groups sidebar section */
  .groups-sidebar {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 10;
    background-color: #f8f8f8;
    transition: transform 0.3s ease;
    overflow-y: auto;
  }

  .dark-mode .groups-sidebar {
    background-color: #222230;
  }

  .groups-sidebar.mobile-hidden {
    transform: translateX(-100%);
  }

  /* Chat area section */
  .chat-area {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 5;
    display: flex;
    flex-direction: column;
    background-color: #fff;
    transition: transform 0.3s ease;
    transform: translateX(100%);
  }

  .dark-mode .chat-area {
    background-color: #2d2d3a;
  }

  .chat-area.mobile-visible {
    transform: translateX(0);
    z-index: 15;
  }

  /* Header styles */
  .groups-header {
    padding: 15px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .dark-mode .groups-header {
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  }

  .groups-header h2 {
    font-size: 1.2rem;
    margin: 0;
  }

  .create-group-btn {
    padding: 8px 12px;
    font-size: 0.9rem;
    border-radius: 8px;
  }

  /* Group items */
  .groups-list {
    padding: 10px;
    overflow-y: auto;
  }

  .group-item {
    padding: 12px;
    margin: 5px 0;
    border-radius: 12px;
    transition: background-color 0.2s;
  }

  .group-avatar {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    margin-right: 12px;
    font-size: 1.1rem;
  }

  .group-name {
    font-size: 0.95rem;
    font-weight: 500;
    margin-bottom: 3px;
  }

  .group-last-message {
    font-size: 0.85rem;
    opacity: 0.8;
    max-width: 180px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  /* Group members count */
  .group-members-count {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 0.8rem;
  }

  /* Chat header */
  .chat-header {
    padding: 12px 15px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    position: relative;
    z-index: 5;
  }

  .dark-mode .chat-header {
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  }

  .back-button {
    background: transparent;
    border: none;
    color: #4a6cf7;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 5px;
    margin-right: 10px;
  }

  .dark-mode .back-button {
    color: #7a9fff;
  }

  .chat-group-name {
    font-size: 1.1rem;
    font-weight: 500;
  }

  /* Group tabs */
  .group-tabs {
    padding: 10px 15px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  }

  .dark-mode .group-tabs {
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  }

  .tab-btn {
    padding: 8px 15px;
    font-size: 0.9rem;
    border-radius: 8px;
  }

  /* Chat messages */
  .chat-messages {
    flex: 1;
    padding: 15px;
    overflow-y: auto;
  }

  .message {
    padding: 10px 12px;
    margin: 5px 0;
    max-width: 80%;
    border-radius: 12px;
  }

  .message-text {
    font-size: 0.95rem;
    line-height: 1.4;
  }

  /* Chat input */
  .chat-input-area {
    padding: 10px;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
  }

  .dark-mode .chat-input-area {
    border-top: 1px solid rgba(255, 255, 255, 0.05);
  }

  .chat-input-container {
    padding: 0 5px;
  }

  .message-input {
    padding: 10px;
    font-size: 0.95rem;
  }

  /* Media attachments */
  .message-image {
    max-width: 180px;
    max-height: 180px;
  }

  .message-video {
    max-width: 180px;
    max-height: 180px;
  }

  /* Group details sidebar */
  .group-details-sidebar {
    width: 100%;
    height: 100%;
    z-index: 20;
  }

  /* Members dialog */
  .members-dialog {
    width: 95%;
    max-width: 350px;
  }
}

/* Group Tabs Navigation */
.group-tabs {
  display: flex;
  padding: 15px 20px 0;
  border-bottom: 1px solid #eaeaea;
  background-color: #fff;
  border-radius: 8px 8px 0 0;
}

.dark-mode .group-tabs {
  border-bottom: 1px solid #3a3a48;
  background-color: #222230;
}

.tab-btn {
  padding: 12px 24px;
  margin-right: 12px;
  margin-bottom: -1px;
  border: none;
  border-radius: 12px 12px 0 0;
  background-color: #f5f5f5;
  color: #6c757d;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 10px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.08);
}

.dark-mode .tab-btn {
  background-color: #2d2d3a;
  color: #a7a7b3;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.2);
}

.tab-btn:hover {
  background-color: #e9ecef;
  color: #4a6cf7;
  transform: translateY(-3px);
  box-shadow: 0 -4px 12px rgba(74, 108, 247, 0.15);
}

.dark-mode .tab-btn:hover {
  background-color: #35354a;
  color: #7a9fff;
  box-shadow: 0 -4px 12px rgba(90, 119, 255, 0.2);
}

.tab-btn.active {
  background-color: #4a6cf7;
  color: white;
  box-shadow: 0 -2px 12px rgba(74, 108, 247, 0.25);
  font-weight: 600;
}

.dark-mode .tab-btn.active {
  background-color: #5a77ff;
  box-shadow: 0 -2px 12px rgba(90, 119, 255, 0.3);
}

.tab-btn.active:hover {
  transform: translateY(-3px);
  box-shadow: 0 -4px 15px rgba(74, 108, 247, 0.3);
}

.tab-btn i {
  font-size: 1.1rem;
}

/* Add these styles to support file uploads and previews */

/* File input styling */
.file-input {
  display: none;
}

.file-preview-container {
  padding: 8px 12px;
  background-color: #f5f5f5;
  border: 1px solid #eee;
  display: inline-flex;
  align-items: center;
  position: relative;
  max-width: fit-content;
  border-radius: 12px;
  margin: 10px 0;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.dark-mode .file-preview-container {
  background-color: #333;
  border: 1px solid #444;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.image-preview,
.video-preview {
  max-width: 200px;
  max-height: 150px;
  overflow: hidden;
  border-radius: 8px;
  margin-right: 0;
  position: relative;
}

.file-preview-container .close-preview {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 22px;
  height: 22px;
  background-color: #ff4757;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  z-index: 2;
  border: none;
}

.dark-mode .file-preview-container .close-preview {
  background-color: #ff6b81;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.image-preview img {
  width: 100%;
  height: auto;
  object-fit: cover;
}

.video-preview video {
  width: 100%;
  height: auto;
}

.file-icon-preview {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #555;
}

.dark-mode .file-icon-preview {
  color: #ddd;
}

.file-icon-preview i {
  font-size: 24px;
  color: #4a6cf7;
}

.dark-mode .file-icon-preview i {
  color: #7a9fff;
}

.file-icon-preview span {
  font-size: 14px;
  max-width: 200px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.cancel-file-button {
  position: absolute;
  right: 15px;
  top: 15px;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  cursor: pointer;
  font-size: 12px;
}

.cancel-file-button:hover {
  background-color: rgba(255, 0, 0, 0.7);
}

/* Message attachment styles */
.message-image-container {
  margin-bottom: 5px;
}

.message-image {
  max-width: 200px;
  max-height: 200px;
  border-radius: 8px;
  cursor: pointer;
}

.message-video-container {
  margin-bottom: 5px;
}

.message-video {
  max-width: 200px;
  max-height: 200px;
  border-radius: 8px;
}

.message-file-container {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 8px;
  margin-bottom: 5px;
  cursor: pointer;
}

.dark-mode .message.incoming .message-file-container {
  background-color: rgba(255, 255, 255, 0.1);
}

.message-file-container i {
  font-size: 18px;
  color: #4a6cf7;
}

.dark-mode .message-file-container i {
  color: #7a9fff;
}

.file-name {
  font-size: 13px;
  max-width: 150px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* GoFile.io styling */
.gofile-badge {
  position: absolute;
  bottom: 5px;
  right: 5px;
  background-color: rgba(74, 137, 220, 0.8);
  color: white;
  font-size: 0.7rem;
  padding: 2px 5px;
  border-radius: 3px;
  display: flex;
  align-items: center;
  gap: 3px;
  z-index: 2;
}

.dark-mode .gofile-badge {
  background-color: rgba(90, 153, 236, 0.8);
}

.gofile-label {
  font-size: 0.7rem;
  background-color: rgba(74, 137, 220, 0.8);
  color: white;
  padding: 2px 5px;
  border-radius: 3px;
  margin-left: 5px;
}

.dark-mode .gofile-label {
  background-color: rgba(90, 153, 236, 0.8);
}

/* Update input container for the new layout */
.input-container {
  display: flex;
  align-items: center;
  width: 100%;
  background-color: #f5f5f5;
  border-radius: 24px;
  padding: 0 5px;
}

.dark-mode .input-container {
  background-color: #35354a;
}

/* Add optimistic message styling */
.message.optimistic {
  opacity: 0.8;
}

.message-status {
  margin-left: 5px;
  display: inline-flex;
}

.message-status i {
  font-size: 0.7rem;
}

.message:hover .delete-message-button {
  opacity: 1;
}

.delete-message-button:hover {
  color: #ff4757;
  transform: scale(1.1);
}

.dark-mode .delete-message-button {
  color: rgba(255, 107, 129, 0.6);
}

.dark-mode .delete-message-button:hover {
  color: #ff6b81;
}

.delete-chat-button {
  background: none;
  border: none;
  padding: 0;
  color: #ff4757;
  cursor: pointer;
  opacity: 0;
  transition: all 0.3s ease;
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.8);
}

.group-item:hover .delete-chat-button {
  opacity: 1;
}

.delete-chat-button:hover {
  background-color: rgba(255, 71, 87, 0.1);
  transform: translateY(-50%) scale(1.1);
}

.dark-mode .delete-chat-button {
  background-color: rgba(42, 42, 58, 0.8);
  color: #ff6b81;
}

.confirm-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.confirm-dialog {
  background-color: white;
  border-radius: 8px;
  width: 400px;
  max-width: 90%;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.dark-mode .confirm-dialog {
  background-color: #2d2d3a;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.confirm-dialog-header {
  padding: 20px;
  border-bottom: 1px solid #eee;
}

.dark-mode .confirm-dialog-header {
  border-bottom: 1px solid #444;
}

.confirm-dialog-header h3 {
  margin: 0;
  color: #333;
  font-size: 1.2rem;
}

.dark-mode .confirm-dialog-header h3 {
  color: #fff;
}

.confirm-dialog-content {
  padding: 20px;
}

.confirm-dialog-content p {
  margin: 0;
  color: #666;
  line-height: 1.5;
}

.dark-mode .confirm-dialog-content p {
  color: #aaa;
}

.confirm-dialog-actions {
  padding: 15px 20px;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.dark-mode .confirm-dialog-actions {
  border-top: 1px solid #444;
}

.btn-cancel,
.btn-danger {
  padding: 8px 16px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn-cancel {
  background-color: #f5f5f5;
  color: #666;
}

.dark-mode .btn-cancel {
  background-color: #3a3a48;
  color: #ccc;
}

.btn-cancel:hover {
  background-color: #eee;
}

.dark-mode .btn-cancel:hover {
  background-color: #444452;
}

.btn-danger {
  background-color: #ff4757;
  color: white;
}

.btn-danger:hover {
  background-color: #ff6b81;
  transform: scale(1.05);
}

.dark-mode .btn-danger {
  background-color: #ff6b81;
}

.modal-title {
  font-size: 1.2rem;
  font-weight: 600;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.2rem;
  color: #666;
  cursor: pointer;
}

.modal-body {
  padding: 20px;
  max-height: 60vh;
  overflow-y: auto;
}

/* Department group option styling */
.department-group-option {
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  padding: 5px 0;
  font-weight: 500;
  color: #333;
  margin-bottom: 10px;
}

.dark-mode .department-group-option {
  color: #e0e0e0;
}

.department-group-option input[type="checkbox"] {
  width: 18px;
  height: 18px;
  cursor: pointer;
  margin: 0;
  position: relative;
  top: 1px;
}

.department-select {
  width: 100%;
  padding: 10px 15px;
  border-radius: 8px;
  border: 1px solid #ddd;
  background-color: #fff;
  color: #333;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.department-select:focus {
  border-color: #4a6cf7;
  box-shadow: 0 0 0 2px rgba(74, 108, 247, 0.2);
  outline: none;
}

.dark-mode .department-select {
  background-color: #35354a;
  border-color: #444;
  color: #e0e0e0;
}

.dark-mode .department-select:focus {
  border-color: #5a77ff;
  box-shadow: 0 0 0 2px rgba(90, 119, 255, 0.2);
}

/* Department group button styling */
.department-group-btn {
  background-color: #28c76f !important;
  box-shadow: 0 4px 10px rgba(40, 199, 111, 0.3) !important;
  transition: all 0.3s ease !important;
}

.department-group-btn:hover {
  background-color: #24b566 !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 6px 15px rgba(40, 199, 111, 0.4) !important;
}

.dark-mode .department-group-btn {
  background-color: #28c76f !important;
  box-shadow: 0 4px 10px rgba(40, 199, 111, 0.2) !important;
}

.dark-mode .department-group-btn:hover {
  background-color: #24b566 !important;
  box-shadow: 0 6px 15px rgba(40, 199, 111, 0.3) !important;
}

.modal-footer {
  padding: 20px;
  border-top: 1px solid #eaeaea;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.btn-primary {
  background-color: #4a6cf7;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
}

.btn-secondary {
  background-color: #e9ecef;
  color: #333;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
}

.btn-danger {
  background-color: #dc3545;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
}

.chat-group-members {
  font-size: 0.9rem;
  color: #6c757d;
  cursor: pointer;
  transition: color 0.3s ease;
}

.chat-group-members:hover {
  color: #4a6cf7;
}

.member-list {
  max-height: 300px;
  overflow-y: auto;
  padding: 10px 0;
}

.members-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(10px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

.members-dialog {
  width: 420px;
  max-width: 90vw;
  max-height: 85vh;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(15px);
  border-radius: 24px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.3),
    0 0 20px rgba(68, 129, 235, 0.15);
  overflow: hidden;
  animation: slideUp 0.4s ease-out;
  display: flex;
  flex-direction: column;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.dark-mode .members-dialog {
  background: rgba(45, 45, 58, 0.98);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.4),
    0 0 20px rgba(74, 108, 247, 0.15);
  border: 1px solid rgba(74, 108, 247, 0.1);
}

.members-dialog-header {
  padding: 22px 24px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(15px);
  position: sticky;
  top: 0;
  z-index: 1;
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.03);
}

.dark-mode .members-dialog-header {
  background: rgba(45, 45, 58, 0.95);
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
}

.members-dialog-title {
  font-size: 1.25rem;
  font-weight: 500;
  color: #333;
  display: flex;
  align-items: center;
  gap: 14px;
  font-family: "Clash Display", sans-serif;
  letter-spacing: 0.3px;
}

.dark-mode .members-dialog-title {
  color: #e9e9e9;
}

.members-dialog-title i {
  color: #4a6cf7;
  font-size: 1.1em;
  background: rgba(74, 108, 247, 0.1);
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(74, 108, 247, 0.15);
  transition: all 0.3s ease;
}

.dark-mode .members-dialog-title i {
  color: #7a9fff;
  background: rgba(122, 159, 255, 0.15);
  box-shadow: 0 2px 8px rgba(122, 159, 255, 0.1);
}

.members-dialog-title i:hover {
  transform: scale(1.05);
  background: rgba(74, 108, 247, 0.15);
}

.dialog-close-btn {
  width: 38px;
  height: 38px;
  border-radius: 50%;
  border: none;
  background: rgba(0, 0, 0, 0.05);
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

.dialog-close-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(
    circle at center,
    rgba(0, 0, 0, 0.1),
    transparent
  );
  opacity: 0;
  transition: opacity 0.3s ease;
}

.dark-mode .dialog-close-btn {
  background: rgba(255, 255, 255, 0.1);
  color: #a7a7b3;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.dark-mode .dialog-close-btn::before {
  background: radial-gradient(
    circle at center,
    rgba(255, 255, 255, 0.1),
    transparent
  );
}

.dialog-close-btn:hover {
  background: rgba(0, 0, 0, 0.08);
  transform: rotate(90deg);
}

.dialog-close-btn:hover::before {
  opacity: 1;
}

.dark-mode .dialog-close-btn:hover {
  background: rgba(255, 255, 255, 0.15);
}

.members-list {
  padding: 20px;
  overflow-y: auto;
  flex: 1;
  scroll-behavior: smooth;
  position: relative;
}

.members-list::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 20px;
  background: linear-gradient(
    to bottom,
    rgba(255, 255, 255, 0.95),
    rgba(255, 255, 255, 0)
  );
  pointer-events: none;
  z-index: 1;
  opacity: 0;
}

.members-list::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 20px;
  background: linear-gradient(
    to top,
    rgba(255, 255, 255, 0.95),
    rgba(255, 255, 255, 0)
  );
  pointer-events: none;
  z-index: 1;
  opacity: 0;
}

.dark-mode .members-list::before {
  background: linear-gradient(
    to bottom,
    rgba(45, 45, 58, 0.95),
    rgba(45, 45, 58, 0)
  );
}

.dark-mode .members-list::after {
  background: linear-gradient(
    to top,
    rgba(45, 45, 58, 0.95),
    rgba(45, 45, 58, 0)
  );
}

.members-list.scrolled-top::before {
  opacity: 1;
}

.members-list.scrolled-bottom::after {
  opacity: 1;
}

.members-list::-webkit-scrollbar {
  width: 6px;
}

.members-list::-webkit-scrollbar-track {
  background: transparent;
}

.members-list::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.15);
  border-radius: 10px;
}

.dark-mode .members-list::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.15);
}

.member-item {
  padding: 16px;
  border-radius: 20px;
  margin-bottom: 12px;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(12px);
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.03);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.03);
  position: relative;
  overflow: hidden;
}

.member-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: linear-gradient(to bottom, #4a6cf7, #05c1ff);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.member-item.admin::before {
  opacity: 1;
}

.dark-mode .member-item {
  background: rgba(60, 60, 75, 0.8);
  border-color: rgba(255, 255, 255, 0.03);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.member-item:hover {
  transform: translateY(-2px);
  background: rgba(255, 255, 255, 0.95);
  border-color: rgba(74, 108, 247, 0.15);
  box-shadow: 0 6px 18px rgba(74, 108, 247, 0.12);
}

.dark-mode .member-item:hover {
  background: rgba(70, 70, 85, 0.95);
  border-color: rgba(122, 159, 255, 0.15);
  box-shadow: 0 6px 18px rgba(0, 0, 0, 0.18);
}

.dark-mode .member-item:hover {
  background: rgba(70, 70, 85, 0.9);
  border-color: rgba(122, 159, 255, 0.15);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.member-avatar {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.1rem;
  font-weight: 600;
  color: white;
  flex-shrink: 0;
  position: relative;
  overflow: hidden;
  border: 2px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 3px 8px rgba(74, 108, 247, 0.2),
    0 0 12px rgba(74, 108, 247, 0.1);
  animation: member-avatar-glow 4s ease-in-out infinite;
}

.member-avatar::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: rotate(45deg);
  animation: member-wave-shine 6s linear infinite,
    member-wave-pulse 8s ease-in-out infinite;
  z-index: 1;
}

.member-avatar::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    145deg,
    rgba(255, 255, 255, 0.3) 0%,
    rgba(255, 255, 255, 0) 60%
  );
  border-radius: 50%;
  z-index: 0;
  animation: member-wave-ripple 10s ease-in-out infinite;
}

.member-info {
  flex: 1;
  min-width: 0;
}

.member-name {
  font-size: 1rem;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dark-mode .member-name {
  color: #e0e0e0;
}

.member-role {
  font-size: 0.85rem;
  color: #666;
  display: flex;
  align-items: center;
  gap: 6px;
  background-color: rgba(0, 0, 0, 0.04);
  padding: 4px 10px;
  border-radius: 20px;
  transition: all 0.3s ease;
}

.dark-mode .member-role {
  color: #a0a0a0;
  background-color: rgba(255, 255, 255, 0.08);
}

.member-role i {
  font-size: 0.9em;
}

.member-role.admin {
  color: #4a6cf7;
  background-color: rgba(74, 108, 247, 0.1);
}

.dark-mode .member-role.admin {
  color: #7a9fff;
  background-color: rgba(122, 159, 255, 0.15);
}

.member-role i.fa-shield-alt {
  color: #4a6cf7;
}

.dark-mode .member-role i.fa-shield-alt {
  color: #7a9fff;
}

.member-status {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 5px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  background: rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  margin-left: auto;
}

.dark-mode .member-status {
  background: rgba(255, 255, 255, 0.05);
}

.status-online {
  color: #2ecc71;
  background: rgba(46, 204, 113, 0.1);
}

.status-offline {
  color: #95a5a6;
  background: rgba(149, 165, 166, 0.1);
}

.dark-mode .status-online {
  color: #2ecc71;
  background: rgba(46, 204, 113, 0.15);
}

.dark-mode .status-offline {
  color: #bdc3c7;
  background: rgba(189, 195, 199, 0.1);
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  position: relative;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
}

.status-online .status-indicator {
  background-color: #4caf50;
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.2);
}

.status-offline .status-indicator {
  background-color: #9e9e9e;
  box-shadow: 0 0 0 3px rgba(158, 158, 158, 0.2);
}

.status-text {
  color: #666;
}

.dark-mode .status-text {
  color: #a0a0a0;
}

.member-status {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 20px;
  font-size: 0.8rem;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  position: relative;
}

.status-online .status-indicator {
  background-color: #4caf50;
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
}

.status-offline .status-indicator {
  background-color: #9e9e9e;
  box-shadow: 0 0 0 2px rgba(158, 158, 158, 0.2);
}

.status-text {
  color: #666;
}

.dark-mode .status-text {
  color: #a0a0a0;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.member-status {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 20px;
  font-size: 0.8rem;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  position: relative;
}

.status-online .status-indicator {
  background-color: #4caf50;
  box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
}

.status-offline .status-indicator {
  background-color: #9e9e9e;
  box-shadow: 0 0 0 2px rgba(158, 158, 158, 0.2);
}

.status-text {
  color: #666;
}

.dark-mode .status-text {
  color: #a0a0a0;
}

/* Group unread badge */
.group-info-meta {
  display: flex;
  align-items: center;
  position: relative;
  flex-wrap: nowrap;
}

.group-members-count {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 0.8rem;
  color: #6c757d;
  background-color: rgba(0, 0, 0, 0.05);
  padding: 3px 8px;
  border-radius: 12px;
  margin-top: 5px;
  margin-left: 5px; /* Add margin to separate from unread count bubble */
}

.dark-mode .group-members-count {
  color: #a7a7b3;
  background-color: rgba(255, 255, 255, 0.1);
}

.unread-count-bubble {
  background-color: #ff4757;
  color: white;
  border-radius: 50%;
  min-width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  padding: 0 4px;
  font-weight: bold;
  box-shadow: 0 2px 4px rgba(255, 71, 87, 0.3);
  margin-top: 5px;
  animation: pulse-animation 1.5s infinite;
  order: -1; /* This will position it before the group-members-count */
  margin-right: 0; /* Remove right margin to position it closer to the group-members-count */
}

@keyframes pulse-animation {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 71, 87, 0.7);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(255, 71, 87, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 71, 87, 0);
  }
}

.dark-mode .unread-count-bubble {
  background-color: #ff6b81;
  box-shadow: 0 2px 4px rgba(255, 107, 129, 0.2);
}
