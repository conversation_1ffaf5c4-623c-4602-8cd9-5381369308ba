/* Base styles and reset */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: "Roboto", "Segoe UI", "Helvetica Neue", Arial, sans-serif;
  font-size: 16px;
  line-height: 1.5;
  color: #333;
  background-color: #f8f9fa;
}

.dark-mode {
  color: #e9e9e9;
  background-color: #212130;
}

/* Import component styles */
@import url("./dashboard.css");
@import url("./sidebar.css");
@import url("./messaging.css");
@import url("./groupchat.css");
@import url("./security.css");
@import url("./admin.css");
@import url("./search.css");

/* Common utility classes */
.text-small {
  font-size: 0.85rem;
}

.text-medium {
  font-size: 1rem;
}

.text-large {
  font-size: 1.15rem;
}

.d-flex {
  display: flex;
}

.justify-between {
  justify-content: space-between;
}

.align-center {
  align-items: center;
}

.flex-column {
  flex-direction: column;
}

.gap-10 {
  gap: 10px;
}

.gap-20 {
  gap: 20px;
}

.mt-10 {
  margin-top: 10px;
}

.mt-20 {
  margin-top: 20px;
}

.mb-10 {
  margin-bottom: 10px;
}

.mb-20 {
  margin-bottom: 20px;
}

.p-10 {
  padding: 10px;
}

.p-20 {
  padding: 20px;
}

/* Button styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  font-size: 0.9rem;
  font-weight: 500;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  outline: none;
}

.btn-primary {
  background-color: #4a6cf7;
  color: white;
}

.dark-mode .btn-primary {
  background-color: #5a77ff;
}

.btn-primary:hover {
  background-color: #385de0;
}

.dark-mode .btn-primary:hover {
  background-color: #4a66e0;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.dark-mode .btn-secondary {
  background-color: #5a6169;
}

.btn-secondary:hover {
  background-color: #5a6268;
}

.dark-mode .btn-secondary:hover {
  background-color: #49505a;
}

.btn-outline {
  background-color: transparent;
  border: 1px solid #ced4da;
  color: #495057;
}

.dark-mode .btn-outline {
  border-color: #3a3a48;
  color: #a7a7b3;
}

.btn-outline:hover {
  background-color: #f8f9fa;
}

.dark-mode .btn-outline:hover {
  background-color: #2d2d3a;
}

.btn-danger {
  background-color: #dc3545;
  color: white;
}

.dark-mode .btn-danger {
  background-color: #e05c68;
}

.btn-danger:hover {
  background-color: #c82333;
}

.dark-mode .btn-danger:hover {
  background-color: #bd4351;
}

.btn-sm {
  padding: 5px 10px;
  font-size: 0.8rem;
}

.btn-lg {
  padding: 10px 20px;
  font-size: 1rem;
}

.btn-icon {
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

/* Form styles */
.form-control {
  width: 100%;
  padding: 10px 15px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  outline: none;
  font-size: 0.9rem;
  transition: border-color 0.3s ease;
}

.dark-mode .form-control {
  background-color: #35354a;
  border-color: #3a3a48;
  color: #e9e9e9;
}

.form-control:focus {
  border-color: #4a6cf7;
  box-shadow: 0 0 0 2px rgba(74, 108, 247, 0.1);
}

.dark-mode .form-control:focus {
  border-color: #5a77ff;
  box-shadow: 0 0 0 2px rgba(90, 119, 255, 0.1);
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #495057;
}

.dark-mode .form-label {
  color: #e9e9e9;
}

.form-group {
  margin-bottom: 20px;
}

.form-error {
  color: #dc3545;
  font-size: 0.8rem;
  margin-top: 5px;
}

.dark-mode .form-error {
  color: #ea868f;
}

/* Card styles */
.card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.dark-mode .card {
  background-color: #2d2d3a;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.card-header {
  padding: 15px 20px;
  border-bottom: 1px solid #eaeaea;
}

.dark-mode .card-header {
  border-bottom: 1px solid #3a3a48;
}

.card-body {
  padding: 20px;
}

.card-footer {
  padding: 15px 20px;
  border-top: 1px solid #eaeaea;
}

.dark-mode .card-footer {
  border-top: 1px solid #3a3a48;
}

/* Alert styles */
.alert {
  padding: 12px 15px;
  border-radius: 4px;
  margin-bottom: 20px;
  font-size: 0.9rem;
}

.alert-success {
  background-color: #d4edda;
  color: #155724;
}

.dark-mode .alert-success {
  background-color: #1e3a29;
  color: #75b798;
}

.alert-danger {
  background-color: #f8d7da;
  color: #721c24;
}

.dark-mode .alert-danger {
  background-color: #3a1c21;
  color: #ea868f;
}

.alert-warning {
  background-color: #fff3cd;
  color: #856404;
}

.dark-mode .alert-warning {
  background-color: #3a341c;
  color: #ffda6a;
}

.alert-info {
  background-color: #d1ecf1;
  color: #0c5460;
}

.dark-mode .alert-info {
  background-color: #1c373a;
  color: #6edff6;
}

/* Modal styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-container {
  background-color: #fff;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 25px rgba(0, 0, 0, 0.1);
}

.dark-mode .modal-container {
  background-color: #2d2d3a;
  box-shadow: 0 4px 25px rgba(0, 0, 0, 0.3);
}

.modal-header {
  padding: 20px;
  border-bottom: 1px solid #eaeaea;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dark-mode .modal-header {
  border-bottom: 1px solid #3a3a48;
}

.modal-title {
  font-size: 1.3rem;
  font-weight: 500;
  color: #333;
  margin: 0;
}

.dark-mode .modal-title {
  color: #e9e9e9;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #6c757d;
}

.dark-mode .modal-close {
  color: #a7a7b3;
}

.modal-body {
  padding: 20px;
}

.modal-footer {
  padding: 20px;
  border-top: 1px solid #eaeaea;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.dark-mode .modal-footer {
  border-top: 1px solid #3a3a48;
}

/* Animation classes */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.slide-in {
  animation: slideIn 0.3s ease-in-out;
}

@keyframes slideIn {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Responsive utilities */
@media (max-width: 768px) {
  .hide-sm {
    display: none !important;
  }
}

@media (max-width: 992px) {
  .hide-md {
    display: none !important;
  }
}

@media (min-width: 769px) {
  .show-sm {
    display: none !important;
  }
}

@media (min-width: 993px) {
  .show-md {
    display: none !important;
  }
}
