/* Modern Messages Page Styling */
.messages-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f9fafc;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
}

.dark-mode .messages-container {
  background-color: #222230;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

.messages-header {
  padding: 18px 20px;
  background-color: #ffffff;
  border-bottom: 1px solid rgba(234, 234, 234, 0.4);
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.03);
  z-index: 5;
}

.dark-mode .messages-header {
  background-color: #2d2d3a;
  border-bottom: 1px solid rgba(58, 58, 72, 0.4);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.messages-header h2 {
  margin: 0;
  font-size: 1.3rem;
  color: #333;
  font-weight: 600;
}

.dark-mode .messages-header h2 {
  color: #e9e9e9;
}

.messages-list {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.message {
  max-width: 70%;
  padding: 12px 18px;
  border-radius: 18px;
  word-wrap: break-word;
  position: relative;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  animation: message-appear 0.3s ease-out;
}

@keyframes message-appear {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message.sent {
  align-self: flex-end;
  background-image: linear-gradient(to right, #4481eb, #04befe);
  color: white;
  border-bottom-right-radius: 4px;
}

.message.received {
  align-self: flex-start;
  background-color: #ffffff;
  color: #333;
  border-bottom-left-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.dark-mode .message.received {
  background-color: #2d2d3a;
  color: #e9e9e9;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.message-content {
  font-size: 0.95rem;
  line-height: 1.5;
}

.message-time {
  font-size: 0.7rem;
  opacity: 0.7;
  margin-top: 5px;
  text-align: right;
}

.message-sender {
  font-size: 0.8rem;
  font-weight: 600;
  margin-bottom: 4px;
  color: #4a6cf7;
}

.dark-mode .message-sender {
  color: #7a9fff;
}

.message-input-container {
  padding: 15px 20px;
  background-color: #ffffff;
  border-top: 1px solid rgba(234, 234, 234, 0.4);
  display: flex;
  align-items: center;
  gap: 12px;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.03);
}

.dark-mode .message-input-container {
  background-color: #2d2d3a;
  border-top: 1px solid rgba(58, 58, 72, 0.4);
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
}

.message-input-container textarea {
  flex: 1;
  padding: 12px 15px;
  border-radius: 24px;
  border: 1px solid #eaeaea;
  resize: none;
  font-size: 0.95rem;
  background-color: #f5f5f5;
  color: #333;
  transition: all 0.3s ease;
  min-height: 48px;
  max-height: 120px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.dark-mode .message-input-container textarea {
  border: 1px solid #3a3a48;
  background-color: #35354a;
  color: #e9e9e9;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.message-input-container textarea:focus {
  outline: none;
  border-color: #4a6cf7;
  box-shadow: 0 0 0 2px rgba(74, 108, 247, 0.15);
}

.dark-mode .message-input-container textarea:focus {
  border-color: #7a9fff;
  box-shadow: 0 0 0 2px rgba(122, 159, 255, 0.15);
}

.message-input-container button {
  padding: 0;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-image: linear-gradient(to right, #4481eb, #04befe);
  color: white;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 3px 8px rgba(74, 108, 247, 0.25);
  flex-shrink: 0;
}

.message-input-container button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 12px rgba(74, 108, 247, 0.35);
}

.message-input-container button i {
  font-size: 18px;
}

.typing-indicator {
  font-size: 0.8rem;
  color: #6c757d;
  margin-bottom: 8px;
  padding-left: 10px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.dark-mode .typing-indicator {
  color: #a7a7b3;
}

.typing-dots {
  display: flex;
  gap: 3px;
}

.typing-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: #6c757d;
  animation: typing-dot 1.4s infinite ease-in-out both;
}

.dark-mode .typing-dot {
  background-color: #a7a7b3;
}

.typing-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing-dot {
  0%,
  80%,
  100% {
    transform: scale(0.6);
  }
  40% {
    transform: scale(1);
  }
}

/* Attachment button and options */
.attachment-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #f5f5f5;
  color: #6c757d;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.dark-mode .attachment-button {
  background-color: #35354a;
  color: #a7a7b3;
}

.attachment-button:hover {
  background-color: #eaeaea;
  color: #4a6cf7;
}

.dark-mode .attachment-button:hover {
  background-color: #44445a;
  color: #7a9fff;
}

/* Emoji picker button */
.emoji-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #f5f5f5;
  color: #6c757d;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.dark-mode .emoji-button {
  background-color: #35354a;
  color: #a7a7b3;
}

.emoji-button:hover {
  background-color: #eaeaea;
  color: #4a6cf7;
}

.dark-mode .emoji-button:hover {
  background-color: #44445a;
  color: #7a9fff;
}

/* Responsive styles */
@media (max-width: 768px) {
  .message {
    max-width: 85%;
  }

  .message-input-container {
    padding: 10px 15px;
  }

  .message-input-container textarea {
    padding: 10px 12px;
    min-height: 42px;
  }

  .message-input-container button {
    width: 42px;
    height: 42px;
  }

  .attachment-button,
  .emoji-button {
    width: 36px;
    height: 36px;
  }
}
