.messaging-container {
  display: flex;
  height: 100%;
  flex: 1;
  background-color: #fff;
  overflow: hidden;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
  position: relative;
  transition: all 0.3s ease;
}

.dark-mode .messaging-container {
  background-color: #222230;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

/* Contacts list */
.contacts-list {
  width: 350px;
  border-right: none;
  display: flex;
  flex-direction: column;
  background-color: #f9f9fa;
  position: relative;
  overflow: hidden;
  box-shadow: 5px 0 15px rgba(0, 0, 0, 0.03);
}

.dark-mode .contacts-list {
  border-right: none;
  background-color: #2d2d3a;
  box-shadow: 5px 0 15px rgba(0, 0, 0, 0.1);
}

.contacts-header {
  padding: 22px 20px 15px 20px;
  border-bottom: none;
  background-color: #f8f9fa;
  box-shadow: none;
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.dark-mode .contacts-header {
  border-bottom: none;
  background-color: #1e1e2d;
  box-shadow: none;
}

.contacts-header h2 {
  margin: 0;
  font-size: 1.3rem;
  color: #333;
  font-weight: 600;
  position: relative;
}

.category-button-container {
  display: flex;
  align-items: center;
  margin-top: 8px;
}

.category-filter-button {
  background: none;
  border: none;
  color: #4a6cf7;
  font-size: 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.category-filter-button:hover {
  background-color: rgba(74, 108, 247, 0.1);
  transform: translateY(-2px);
}

.dark-mode .category-filter-button {
  color: #6d8dff;
}

.dark-mode .category-filter-button:hover {
  background-color: rgba(109, 141, 255, 0.2);
}

.dark-mode .contacts-header h2 {
  color: #e9e9e9;
}

.contacts-search {
  padding: 15px 50px;
  background-color: #fff;
  border-bottom: 1px solid #eaeaea;
  position: relative;
  z-index: 1;
}

.dark-mode .contacts-search {
  background-color: #222230;
  border-bottom: 1px solid #3a3a48;
}

.search-bar {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 20px;
  padding: 14px 18px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  border: 1px solid transparent;
  position: relative;
}

.search-bar:focus-within {
  box-shadow: 0 4px 12px rgba(68, 129, 235, 0.15);
  border-color: rgba(68, 129, 235, 0.2);
  transform: translateY(-1px);
}

.dark-mode .search-bar {
  background-color: #35354a;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.dark-mode .search-bar:focus-within {
  box-shadow: 0 4px 12px rgba(4, 190, 254, 0.2);
  border-color: rgba(4, 190, 254, 0.3);
}

.search-bar i {
  color: #6c757d;
  margin-right: 10px;
}

.dark-mode .search-bar i {
  color: #a7a7b3;
}

.search-bar input {
  border: none;
  background: transparent;
  width: 100%;
  outline: none;
  color: #333;
}

.dark-mode .search-bar input {
  color: #e9e9e9;
}

.search-bar input::placeholder {
  color: #adb5bd;
}

.dark-mode .search-bar input::placeholder {
  color: #6c7293;
}

.contacts-items {
  flex: 1;
  overflow-y: auto;
  padding: 15px 0;
  margin-top: 0;
  background-color: #f9f9fa;
}

.dark-mode .contacts-items {
  background-color: #2d2d3a;
}

.contact-item {
  display: flex;
  align-items: center;
  padding: 16px 18px;
  margin: 8px 12px;
  border-radius: 18px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  border: 1px solid transparent;
  background-color: #fff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.04);
  position: relative;
  animation-fill-mode: both;
}

.dark-mode .contact-item {
  background-color: #2a2a3a;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
}

.contact-item:hover {
  background-color: #f7f9ff;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

.dark-mode .contact-item:hover {
  background-color: #303045;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.12);
}

.contact-item.active {
  background-color: #eef2ff;
  border-color: rgba(74, 108, 247, 0.2);
  box-shadow: 0 4px 10px rgba(74, 108, 247, 0.08);
}

.dark-mode .contact-item.active {
  background-color: #2a2a45;
  border-color: rgba(122, 159, 255, 0.3);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
}

.contact-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  margin-right: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  color: #fff;
  flex-shrink: 0;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15), 0 0 12px rgba(68, 129, 235, 0.25);
  position: relative;
  transition: all 0.3s ease;
  overflow: hidden;
  animation: contact-avatar-glow 4s ease-in-out infinite;
  border: 2px solid rgba(255, 255, 255, 0.3);
  /* Remove default background-image to allow custom colors */
}

.contact-item:hover .contact-avatar {
  transform: scale(1.05);
  animation: contact-avatar-glow-hover 2s ease-in-out infinite;
}

.contact-avatar::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: rotate(45deg);
  animation: contact-wave-shine 6s linear infinite,
    contact-wave-pulse 8s ease-in-out infinite;
  z-index: 1;
}

.contact-avatar::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    145deg,
    rgba(255, 255, 255, 0.3) 0%,
    rgba(255, 255, 255, 0) 60%
  );
  border-radius: 50%;
  z-index: 0;
  animation: contact-wave-ripple 10s ease-in-out infinite;
}

.contact-avatar-wrapper {
  position: relative;
  margin-right: 16px;
}

.contact-status {
  position: absolute;
  bottom: -2px;
  right: 7px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid #fff;
  background-color: #ccc;
  transition: all 0.3s ease;
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.1);
  z-index: 2;
}

.dark-mode .contact-status {
  border-color: #2a2a3a;
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.2);
}

.contact-status.active {
  background-color: #4caf50;
  box-shadow: 0 0 4px rgba(76, 175, 80, 0.4);
}

.contact-item.active .contact-status.active {
  box-shadow: 0 0 5px rgba(76, 175, 80, 0.5);
}

.contact-info {
  flex: 1;
  min-width: 0;
  margin-right: 8px;
  position: relative;
}

.contact-name-row {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  flex-wrap: nowrap;
  margin-bottom: 5px;
  gap: 6px;
  justify-content: space-between;
  width: 100%;
}

.contact-name-row .contact-name {
  flex: 1;
  min-width: 0;
  margin-right: auto;
}

.contact-name {
  font-weight: 600;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  transition: color 0.3s ease;
}

.dark-mode .contact-name {
  color: #e9e9e9;
}

.contact-item:hover .contact-name {
  color: #4a6cf7;
}

.dark-mode .contact-item:hover .contact-name {
  color: #7a9fff;
}

.contact-category-badge {
  display: flex;
  align-items: center;
  padding: 2px 6px;
  border-radius: 10px;
  background-color: rgba(0, 0, 0, 0.05);
  font-size: 0.7rem;
  max-width: 100px;
  position: relative;
  overflow: hidden;
  height: 18px;
}

.dark-mode .contact-category-badge {
  background-color: rgba(255, 255, 255, 0.1);
}

.contact-category-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 4px;
  box-shadow: 0 0 3px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.dark-mode .contact-category-indicator {
  box-shadow: 0 0 3px rgba(0, 0, 0, 0.3);
}

/* Wave animation for category indicator */
.contact-category-indicator::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.4) 50%,
    transparent 100%
  );
  opacity: 0;
  pointer-events: none;
  animation: categoryIndicatorWaveGlow 5s infinite;
  border-radius: 50%;
  z-index: 1;
}

/* Internal glow effect for category indicator */
.contact-category-indicator::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.2);
  opacity: 0;
  border-radius: 50%;
  pointer-events: none;
  animation: categoryIndicatorPulseGlow 4s infinite;
  z-index: 0;
}

/* Wave animation for category indicator */
@keyframes categoryIndicatorWaveGlow {
  0% {
    opacity: 0;
    transform: translateX(-50%);
  }
  50% {
    opacity: 0.7;
    transform: translateX(0%);
  }
  100% {
    opacity: 0;
    transform: translateX(50%);
  }
}

/* Pulse animation for category indicator */
@keyframes categoryIndicatorPulseGlow {
  0% {
    opacity: 0;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 0;
  }
}

.contact-category-name {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #666;
  font-weight: 500;
}

.dark-mode .contact-category-name {
  color: #aaa;
}

/* Styles for multiple categories */
.contact-additional-categories {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  flex-wrap: nowrap;
  gap: 4px;
  min-height: 16px; /* Ensure consistent height even when empty */
  justify-content: center;
}

.contact-category-badge.additional {
  padding: 1px 4px;
  border-radius: 8px;
  font-size: 0.65rem;
  max-width: 80px;
  height: 16px;
  margin-right: 2px;
  display: inline-flex;
  align-items: center;
}

.more-categories-badge {
  font-size: 0.65rem;
  color: #666;
  background-color: rgba(0, 0, 0, 0.05);
  padding: 1px 4px;
  border-radius: 8px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  align-self: flex-end;
}

.dark-mode .more-categories-badge {
  color: #aaa;
  background-color: rgba(255, 255, 255, 0.1);
}

.contact-last-message {
  font-size: 0.85rem;
  color: #6c757d;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 180px;
}

.dark-mode .contact-last-message {
  color: #a7a7b3;
}

.contact-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 5px;
}

.contact-time {
  font-size: 0.75rem;
  color: #6c757d;
}

.dark-mode .contact-time {
  color: #a7a7b3;
}

.unread-badge {
  background-color: #4a6cf7;
  color: white;
  min-width: 20px;
  height: 20px;
  border-radius: 10px;
  font-size: 0.7rem;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 5px;
  box-shadow: 0 2px 4px rgba(74, 108, 247, 0.15);
  font-weight: bold;
  margin-top: 0px;
  margin-right: 5px; /* Add margin to separate from category icon */
  order: -1; /* This will position it before the category icon */
}

.dark-mode .unread-badge {
  background-color: #04befe;
  box-shadow: 0 2px 4px rgba(74, 108, 247, 0.1);
}

/* Add styles for the contact-category-wrapper */
.contact-category-wrapper {
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
}

.delete-chat-button {
  position: absolute;
  top: 50%;
  right: 12px;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.8);
  color: #ff4757;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  opacity: 0;
  transition: all 0.3s ease;
  z-index: 2;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transform: translateY(-50%);
}

.dark-mode .delete-chat-button {
  background-color: rgba(42, 42, 58, 0.8);
  color: #ff6b81;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.contact-item:hover .delete-chat-button {
  opacity: 1;
}

.delete-chat-button:hover {
  background-color: #ff4757;
  color: white;
  transform: translateY(-50%) rotate(90deg);
}

.dark-mode .delete-chat-button:hover {
  background-color: #ff6b81;
}

/* Chat area */
.chat-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  background-color: #fff;
  overflow: hidden;
}

.dark-mode .chat-area {
  background-color: #222;
}

/* Chat Details Sidebar */
.chat-details-sidebar {
  width: 280px;
  background-color: #f8f8f8;
  border-left: 1px solid #eaeaea;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  animation: slide-in-right 0.3s ease-out;
}

@keyframes slide-in-right {
  from {
    transform: translateX(100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes contact-wave-shine {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
  }
}

@keyframes contact-avatar-glow {
  0% {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15),
      0 0 12px rgba(68, 129, 235, 0.25);
  }
  50% {
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2), 0 0 18px rgba(68, 129, 235, 0.4);
  }
  100% {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15),
      0 0 12px rgba(68, 129, 235, 0.25);
  }
}

@keyframes contact-avatar-glow-hover {
  0% {
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15), 0 0 15px rgba(68, 129, 235, 0.3);
  }
  50% {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.25),
      0 0 20px rgba(68, 129, 235, 0.5);
  }
  100% {
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15), 0 0 15px rgba(68, 129, 235, 0.3);
  }
}

@keyframes contact-wave-pulse {
  0% {
    opacity: 0.7;
    width: 200%;
    height: 200%;
  }
  50% {
    opacity: 0.9;
    width: 220%;
    height: 220%;
  }
  100% {
    opacity: 0.7;
    width: 200%;
    height: 200%;
  }
}

@keyframes contact-wave-ripple {
  0% {
    transform: scale(1);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.9;
  }
  100% {
    transform: scale(1);
    opacity: 0.7;
  }
}

.dark-mode .chat-details-sidebar {
  background-color: #2d2d3a;
  border-left: 1px solid #3a3a48;
}

.chat-details-header {
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #eaeaea;
  background-color: #fff;
}

.dark-mode .chat-details-header {
  border-bottom: 1px solid #3a3a48;
  background-color: #222230;
}

.chat-details-header h3 {
  margin: 0;
  font-size: 1.2rem;
  color: #333;
}

.dark-mode .chat-details-header h3 {
  color: #e9e9e9;
}

.close-details-button {
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  font-size: 1.2rem;
  transition: all 0.3s ease;
}

.dark-mode .close-details-button {
  color: #a7a7b3;
}

.close-details-button:hover {
  color: #4a6cf7;
  transform: rotate(90deg);
}

.dark-mode .close-details-button:hover {
  color: #7a9fff;
}

.chat-details-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.chat-details-section {
  margin-bottom: 25px;
}

.section-title {
  font-size: 0.9rem;
  color: #6c757d;
  margin-bottom: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  text-align: center;
}

.dark-mode .section-title {
  color: #a7a7b3;
}

.contact-profile {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30px;
}

.contact-avatar.large {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  font-size: 32px;
  animation: contact-avatar-large-glow 4s ease-in-out infinite;
  overflow: hidden;
}

.contact-profile .contact-avatar-wrapper {
  margin: 0 auto 15px;
  position: relative;
  width: 80px;
  height: 80px;
}

.contact-profile .contact-status {
  width: 16px;
  height: 16px;
  border: 2px solid #fff;
  bottom: -1px;
  right: -1px;
}

.dark-mode .contact-profile .contact-status {
  border-color: #2a2a3a;
}

.contact-avatar.large::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.1) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: rotate(45deg);
  animation: contact-wave-shine 6s linear infinite,
    contact-wave-pulse 8s ease-in-out infinite;
  z-index: 1;
}

.contact-avatar.large::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    145deg,
    rgba(255, 255, 255, 0.2) 0%,
    rgba(255, 255, 255, 0) 60%
  );
  border-radius: 50%;
  z-index: 0;
  animation: contact-wave-ripple 10s ease-in-out infinite;
}

@keyframes contact-avatar-large-glow {
  0% {
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  }
  50% {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.25);
  }
  100% {
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  }
}

.contact-profile .contact-name {
  font-size: 1.2rem;
  margin: 10px 0 5px;
  color: #333;
}

.dark-mode .contact-profile .contact-name {
  color: #e9e9e9;
}

.contact-status-text {
  font-size: 0.9rem;
  color: #6c757d;
  margin-bottom: 15px;
}

.dark-mode .contact-status-text {
  color: #a7a7b3;
}

.chat-actions {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.centered-actions {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.centered-button {
  width: 80%;
  margin: 0 auto;
}

.search-conversation-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  position: relative;
  margin-bottom: 15px;
}

.chat-search-bar {
  width: 90%;
  margin: 5px auto;
  background-color: #f5f5f5;
  border-radius: 12px;
  padding: 10px 15px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  max-height: 0;
  opacity: 0;
  transition: all 0.3s ease;
}

.dark-mode .chat-search-bar {
  background-color: #35354a;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.chat-search-bar.active {
  max-height: 50px;
  opacity: 1;
  margin: 10px auto;
}

.chat-search-bar i {
  color: #777;
  margin-right: 10px;
  font-size: 0.9rem;
}

.dark-mode .chat-search-bar i {
  color: #aaa;
}

.chat-search-bar input {
  flex: 1;
  border: none;
  background: transparent;
  outline: none;
  font-size: 0.9rem;
  color: #333;
}

.dark-mode .chat-search-bar input {
  color: #e0e0e0;
}

.chat-search-bar input::placeholder {
  color: #999;
}

.dark-mode .chat-search-bar input::placeholder {
  color: #777;
}

/* Search highlight */
.search-highlight {
  position: relative;
  animation: search-pulse 2s infinite;
  box-shadow: 0 0 8px rgba(74, 108, 247, 0.5);
}

@keyframes search-pulse {
  0% {
    box-shadow: 0 0 8px rgba(74, 108, 247, 0.5);
  }
  50% {
    box-shadow: 0 0 12px rgba(74, 108, 247, 0.8);
  }
  100% {
    box-shadow: 0 0 8px rgba(74, 108, 247, 0.5);
  }
}

.group-action-button {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  border-radius: 12px;
  border: none;
  background-color: #f5f5f5;
  color: #555;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  width: 80%;
  text-align: left;
  position: relative;
  overflow: hidden;
}

.dark-mode .group-action-button {
  background-color: #35354a;
  color: #a7a7b3;
}

.group-action-button i {
  margin-right: 10px;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.group-action-button:hover {
  background-color: #eef2ff;
  color: #4a6cf7;
  transform: translateY(-2px);
}

.dark-mode .group-action-button:hover {
  background-color: #2a2a45;
  color: #7a9fff;
}

.group-action-button.active {
  background-color: #eef2ff;
  color: #4a6cf7;
}

.dark-mode .group-action-button.active {
  background-color: #2a2a45;
  color: #7a9fff;
}

.group-action-button.active i {
  color: #4a6cf7;
}

.dark-mode .group-action-button.active i {
  color: #7a9fff;
}

.action-divider {
  width: 80%;
  height: 1px;
  background-color: #eee;
  margin: 5px 0;
}

.dark-mode .action-divider {
  background-color: #444;
}

.chat-action-button.centered-button {
  display: flex;
  align-items: center;
  padding: 12px 13px;
  border-radius: 12px;
  border: none;
  background-color: #f5f5f5;
  color: #555;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  width: 100%;
  text-align: center;
  justify-content: center;
  position: relative;
}

.dark-mode .chat-action-button.centered-button {
  background-color: #35354a;
  color: #a7a7b3;
}

.chat-action-button.centered-button i {
  margin-right: 10px;
  font-size: 1rem;
}

.chat-action-button.centered-button:hover {
  background-color: #eef2ff;
  color: #4a6cf7;
  transform: translateY(-2px);
}

.dark-mode .chat-action-button.centered-button:hover {
  background-color: #2a2a45;
  color: #7a9fff;
}

.delete-messages-button {
  display: flex;
  align-items: center;
  padding: 12px 15px;
  border-radius: 12px;
  border: none;
  background-color: rgba(255, 71, 87, 0.1);
  color: #ff4757;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  width: 100%;
  text-align: left;
  margin-top: 10px;
}

.dark-mode .delete-messages-button {
  background-color: rgba(255, 107, 129, 0.1);
  color: #ff6b81;
}

.delete-messages-button i {
  margin-right: 10px;
  font-size: 1rem;
}

.delete-messages-button:hover {
  background-color: rgba(255, 71, 87, 0.2);
  transform: translateY(-2px);
}

.dark-mode .delete-messages-button:hover {
  background-color: rgba(255, 107, 129, 0.2);
}

.chat-header {
  padding: 12px 15px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #eee;
  background-color: #fff;
  z-index: 5;
  margin-top: 0;
  padding-top: 12px;
}

.dark-mode .chat-header {
  border-bottom: 1px solid #444;
  background-color: #222;
}

.chat-contact-info {
  display: flex;
  align-items: center;
}

.chat-contact-details {
  margin-left: 10px;
}

.chat-contact-name {
  font-weight: 600;
  font-size: 1rem;
  color: #333;
}

.dark-mode .chat-contact-name {
  color: #e9e9e9;
}

.chat-contact-info-row {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 2px;
}

.chat-contact-status {
  font-size: 0.75rem;
  color: #28a745;
}

.dark-mode .chat-contact-status {
  color: #4cd964;
}

.chat-contact-department {
  display: flex;
  align-items: center;
  gap: 5px;
}

.department-label {
  font-size: 0.75rem;
  color: #777;
}

.dark-mode .department-label {
  color: #aaa;
}

.department-value {
  font-size: 0.75rem;
  padding: 2px 8px;
  border-radius: 12px;
  color: white;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.dark-mode .department-value {
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Department glow effect */
.department-glow-effect {
  position: relative;
  overflow: hidden;
  z-index: 1;
}

/* Wave animation for department name */
.department-glow-effect::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.4) 50%,
    transparent 100%
  );
  opacity: 0;
  pointer-events: none;
  animation: departmentWaveGlow 5s infinite;
  border-radius: 12px;
  clip-path: inset(0 0 0 0 round 12px);
  z-index: 1;
}

/* Internal glow effect */
.department-glow-effect::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.2);
  opacity: 0;
  border-radius: 12px;
  pointer-events: none;
  animation: departmentPulseGlow 4s infinite;
  clip-path: inset(0 0 0 0 round 12px);
  z-index: 0;
}

/* Wave animation for department glow */
@keyframes departmentWaveGlow {
  0% {
    opacity: 0;
    transform: translateX(-50%);
  }
  50% {
    opacity: 0.7;
    transform: translateX(0%);
  }
  100% {
    opacity: 0;
    transform: translateX(50%);
  }
}

/* Pulse animation for department glow */
@keyframes departmentPulseGlow {
  0% {
    opacity: 0;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 0;
  }
}

.chat-actions {
  display: flex;
  gap: 10px;
}

.chat-header .chat-action-button {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border: none;
  background-color: #f5f5f5;
  color: #555;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.dark-mode .chat-header .chat-action-button {
  background-color: #333;
  color: #aaa;
}

.chat-header .chat-action-button:hover {
  background-color: #e0e0e0;
  color: #4a6cf7;
}

.dark-mode .chat-header .chat-action-button:hover {
  background-color: #444;
  color: #7a9fff;
}

.chat-messages {
  flex: 1;
  padding: 15px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 10px;
  background-color: #f5f5f5;
  animation-fill-mode: both;
}

.dark-mode .chat-messages {
  background-color: #1a1a1a;
}

.message {
  max-width: 70%;
  padding: 10px 15px;
  border-radius: 18px;
  position: relative;
  word-break: break-word;
  cursor: pointer;
}

.message.optimistic {
  opacity: 0.9;
}

.message.incoming {
  align-self: flex-start;
  background-color: #fff;
  color: #333;
  border-bottom-left-radius: 5px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.dark-mode .message.incoming {
  background-color: #333;
  color: #e0e0e0;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.message.outgoing {
  align-self: flex-end;
  background-color: #4a6cf7;
  color: #fff;
  border-bottom-right-radius: 5px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.message.deleted .message-text {
  background-color: #f1f1f1;
  color: #999;
  font-style: italic;
}

.dark-mode .message.deleted .message-text {
  background-color: #3a3a48;
  color: #888;
}

/* Message options menu */
.message-options-trigger {
  position: absolute;
  right: 8px;
  top: 8px;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  opacity: 0;
  transition: opacity 0.2s ease, background-color 0.2s ease;
  background-color: rgba(0, 0, 0, 0.05);
  z-index: 5;
}

.dark-mode .message-options-trigger {
  background-color: rgba(255, 255, 255, 0.1);
}

.message:hover .message-options-trigger {
  opacity: 1;
}

.message-options-trigger:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.dark-mode .message-options-trigger:hover {
  background-color: rgba(255, 255, 255, 0.15);
}

.message-options {
  position: absolute;
  top: -40px;
  right: 0;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  display: flex;
  overflow: hidden;
  z-index: 10;
}

.message-options-vertical {
  top: 30px;
  right: 30px;
  transform: none;
}

.dark-mode .message-options {
  background-color: #2d2d3a;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.message-option-btn {
  border: none;
  background: none;
  padding: 8px 12px;
  cursor: pointer;
  color: #333;
  font-size: 0.85rem;
  display: flex;
  align-items: center;
  gap: 5px;
  transition: background-color 0.2s;
  flex-direction: row;
}

.dark-mode .message-option-btn {
  color: #e9e9e9;
}

.message-option-btn:hover {
  background-color: #f5f5f5;
}

.dark-mode .message-option-btn:hover {
  background-color: #3a3a48;
}

.message-option-btn.delete {
  color: #ff4757;
}

.dark-mode .message-option-btn.delete {
  color: #ff6b81;
}

/* Edit message styles */
.edit-message-container {
  width: 100%;
  margin-bottom: 5px;
}

.edit-message-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 0.9rem;
  margin-bottom: 5px;
}

.dark-mode .edit-message-input {
  background-color: #35354a;
  border-color: #444;
  color: #e9e9e9;
}

.edit-message-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.edit-save-btn,
.edit-cancel-btn {
  border: none;
  padding: 5px 10px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 0.8rem;
  display: flex;
  align-items: center;
  gap: 5px;
}

.edit-save-btn {
  background-color: #4a6cf7;
  color: white;
}

.dark-mode .edit-save-btn {
  background-color: #4a6cf7;
}

.edit-cancel-btn {
  background-color: #f1f1f1;
  color: #666;
}

.dark-mode .edit-cancel-btn {
  background-color: #3a3a48;
  color: #ccc;
}

.dark-mode .message.outgoing {
  background-color: #4a6cf7;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.message-text {
  margin-bottom: 5px;
}

.message-time {
  font-size: 0.7rem;
  opacity: 0.7;
  text-align: right;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 5px;
}

.edited-label {
  font-size: 0.65rem;
  color: #999;
  font-style: italic;
}

.message-status {
  margin-left: 5px;
  display: inline-flex;
}

.message-status i {
  font-size: 0.7rem;
}

.chat-input-area {
  padding: 10px 15px;
  background-color: #fff;
  border-top: 1px solid #eee;
  z-index: 5;
}

.dark-mode .chat-input-area {
  background-color: #222;
  border-top: 1px solid #444;
}

.input-container {
  display: flex;
  align-items: center;
  width: 100%;
  background-color: #f5f5f5;
  border-radius: 24px;
  padding: 0 5px;
}

.dark-mode .input-container {
  background-color: #35354a;
}

.chat-input-area input {
  flex: 1;
  border: none;
  outline: none;
  padding: 10px 15px;
  background-color: transparent;
  color: #333;
  font-size: 1rem;
}

.dark-mode .chat-input-area input {
  color: #e0e0e0;
}

.chat-input-area input::placeholder {
  color: #999;
}

.dark-mode .chat-input-area input::placeholder {
  color: #777;
}

.attachment-button,
.send-button,
.emoji-button {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: transparent;
  margin: 0 2px;
}

.attachment-button {
  color: #4a89dc;
  font-size: 1.1rem;
}

.send-button {
  color: #4a6cf7;
}

.dark-mode .attachment-button {
  color: #5a99ec;
}

.dark-mode .send-button {
  color: #aaa;
}

.attachment-button:hover {
  background-color: rgba(74, 137, 220, 0.1);
  color: #3a79cc;
  transform: translateY(-2px);
}

/* File upload indicator */
.attachment-button.uploading {
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

.send-button:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: #4a6cf7;
}

.dark-mode .attachment-button:hover {
  background-color: rgba(90, 153, 236, 0.15);
  color: #7ab0ff;
}

.dark-mode .send-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: #7a9fff;
}

.dark-mode .send-button {
  color: #7a9fff;
}

.send-button:hover {
  background-color: #4a6cf7;
  color: #fff;
}

.dark-mode .send-button:hover {
  background-color: #4a6cf7;
  color: #fff;
}

.no-chat-selected {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #6c757d;
  text-align: center;
  padding: 20px;
  animation-fill-mode: both;
}

.dark-mode .no-chat-selected {
  color: #a7a7b3;
}

.no-chat-icon {
  font-size: 3rem;
  margin-bottom: 20px;
  color: #ccc;
  animation-fill-mode: both;
}

.dark-mode .no-chat-icon {
  color: #555;
}

.no-chat-selected h3 {
  margin-bottom: 10px;
  color: #555;
  animation-fill-mode: both;
}

.dark-mode .no-chat-selected h3 {
  color: #ccc;
}

.no-chat-selected p {
  color: #888;
  animation-fill-mode: both;
}

.dark-mode .no-chat-selected p {
  color: #777;
}

@media (max-width: 1200px) {
  .messaging-container {
    height: calc(100vh - 20px);
  }

  .contacts-list {
    width: 300px;
  }

  .contact-last-message {
    max-width: 150px;
  }
}

@media (max-width: 992px) {
  .chat-details-sidebar {
    position: fixed;
    right: 0;
    top: 0;
    bottom: 0;
    width: 300px;
    z-index: 10;
    background-color: #fff;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
  }

  .dark-mode .chat-details-sidebar {
    background-color: #2d2d3a;
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.2);
  }

  .chat-details-sidebar.open {
    transform: translateX(0);
  }

  .contacts-list {
    width: 280px;
  }

  .chat-area {
    flex: 1;
  }

  .contact-last-message {
    max-width: 120px;
  }

  .chat-input-container {
    padding: 12px;
  }

  .message-input {
    padding: 10px 12px;
  }
}

@media (max-width: 768px) {
  .messaging-container {
    height: calc(100vh - 20px);
    overflow: hidden;
    position: relative;
  }

  .contacts-list {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 10;
    background-color: #f9f9fa;
    transition: transform 0.3s ease-in-out;
    transform: translateX(0);
  }

  .dark-mode .contacts-list {
    background-color: #2d2d3a;
  }

  .contacts-list.mobile-hidden {
    transform: translateX(-100%);
  }

  .chat-area {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 5;
    display: flex;
    flex-direction: column;
    background-color: #fff;
    transition: transform 0.3s ease-in-out;
    transform: translateX(100%);
    margin-top: 0;
    padding-top: 0;
  }

  .dark-mode .chat-area {
    background-color: #222230;
  }

  .chat-area.mobile-visible {
    transform: translateX(0);
    z-index: 15;
  }

  .chat-messages {
    flex: 1;
    overflow-y: auto;
  }

  .chat-details-sidebar {
    width: 100%;
    height: 100vh;
    z-index: 20;
  }

  .contact-item {
    padding: 10px 12px;
    margin: 4px 8px;
  }

  .contact-avatar {
    width: 40px;
    height: 40px;
    margin-right: 10px;
    border-radius: 50%;
  }

  .message-input {
    font-size: 0.95rem;
  }

  .chat-input-actions {
    gap: 8px;
  }

  .back-button {
    background: transparent;
    border: none;
    color: #4a6cf7;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 5px 10px;
    margin-right: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
  }

  .dark-mode .back-button {
    color: #7a9fff;
  }

  .back-button:hover {
    transform: translateX(-3px);
  }

  .chat-header {
    display: flex;
    align-items: center;
  }
}

@media (max-width: 576px) {
  .messaging-container {
    display: flex;
    flex-direction: column;
    height: calc(100vh - 60px);
    margin-top: 60px;
    overflow: hidden;
    position: relative;
  }

  /* Fix for send button on very narrow screens */
  .input-container {
    padding: 0 2px;
  }

  .attachment-button,
  .emoji-button,
  .send-button {
    min-width: 32px;
    width: 32px;
    height: 32px;
    margin: 0 1px;
  }

  /* Contacts list section */
  .contacts-list {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 10;
    background-color: #f8f8f8;
    transition: transform 0.3s ease;
    overflow-y: auto;
  }

  .dark-mode .contacts-list {
    background-color: #222230;
  }

  .contacts-list.mobile-hidden {
    transform: translateX(-100%);
  }

  /* Chat area section */
  .chat-area {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 5;
    display: flex;
    flex-direction: column;
    background-color: #fff;
    transition: transform 0.3s ease;
    transform: translateX(100%);
    padding-bottom: 0;
    margin-bottom: 0;
  }

  .dark-mode .chat-area {
    background-color: #2d2d3a;
  }

  .chat-area.mobile-visible {
    transform: translateX(0);
    z-index: 15;
  }

  /* Header styles */
  .contacts-header {
    padding: 15px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  }

  .dark-mode .contacts-header {
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  }

  .contacts-header h2 {
    font-size: 1.2rem;
    margin: 0;
  }

  /* Contact items */
  .contacts-items {
    padding: 10px;
    overflow-y: auto;
  }

  .contact-item {
    padding: 12px;
    margin: 5px 0;
    border-radius: 12px;
    transition: background-color 0.2s;
  }

  .contact-avatar-wrapper {
    position: relative;
  }

  .contact-avatar {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    margin-right: 12px;
    font-size: 1.1rem;
  }

  .contact-status {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    position: absolute;
    bottom: 0;
    right: 8px;
    border: 2px solid #f8f8f8;
  }

  .dark-mode .contact-status {
    border-color: #222230;
  }

  .contact-name {
    font-size: 0.95rem;
    font-weight: 500;
    margin-bottom: 3px;
  }

  .contact-last-message {
    font-size: 0.85rem;
    opacity: 0.8;
    max-width: 180px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  /* Chat header */
  .chat-header {
    padding: 12px 15px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  }

  .dark-mode .chat-header {
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  }

  .back-button {
    background: transparent;
    border: none;
    color: #4a6cf7;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 5px;
    margin-right: 10px;
  }

  .dark-mode .back-button {
    color: #7a9fff;
  }

  .chat-contact-name {
    font-size: 1.1rem;
    font-weight: 500;
  }

  /* Chat messages */
  .chat-messages {
    flex: 1;
    padding: 15px;
    overflow-y: auto;
  }

  .message {
    padding: 10px 12px;
    margin: 5px 0;
    max-width: 80%;
    border-radius: 12px;
  }

  .message-text {
    font-size: 0.95rem;
    line-height: 1.4;
  }

  /* Chat input */
  .chat-input-area {
    padding: 10px;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
    margin-bottom: 0;
    padding-bottom: 10px;
  }

  .dark-mode .chat-input-area {
    border-top: 1px solid rgba(255, 255, 255, 0.05);
  }

  .chat-input-container {
    padding: 0 5px;
  }

  .message-input {
    padding: 10px;
    font-size: 0.95rem;
  }

  .chat-input-actions {
    gap: 8px;
  }

  .chat-input-actions button {
    width: 36px;
    height: 36px;
  }

  /* Media attachments */
  .message-image {
    max-width: 180px;
    max-height: 180px;
  }

  .message-video {
    max-width: 180px;
    max-height: 180px;
  }

  /* Dialogs */
  .confirm-dialog {
    width: 90%;
    max-width: 300px;
  }

  .confirm-dialog-header {
    padding: 15px;
  }

  .confirm-dialog-content {
    padding: 15px;
  }

  .confirm-dialog-actions {
    padding: 12px 15px;
  }
}

/* File input styling */
.file-input {
  position: absolute;
  width: 0;
  height: 0;
  opacity: 0;
  z-index: -1;
}

.file-preview-container {
  padding: 8px 12px;
  background-color: #f5f5f5;
  border: 1px solid #eee;
  display: inline-flex;
  align-items: center;
  position: relative;
  max-width: fit-content;
  border-radius: 12px;
  margin: 10px 0;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease;
}

.dark-mode .file-preview-container {
  background-color: #333;
  border: 1px solid #444;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.image-preview,
.video-preview {
  max-width: 200px;
  max-height: 150px;
  overflow: hidden;
  border-radius: 8px;
  margin-right: 0;
  position: relative;
}

.file-preview-container .close-preview {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 22px;
  height: 22px;
  background-color: #ff4757;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  z-index: 2;
  border: none;
}

.dark-mode .file-preview-container .close-preview {
  background-color: #ff6b81;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.image-preview img {
  width: 100%;
  height: auto;
  object-fit: cover;
}

.video-preview video {
  width: 100%;
  height: auto;
}

.file-icon-preview {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #555;
}

.dark-mode .file-icon-preview {
  color: #ddd;
}

.file-icon-preview i {
  font-size: 24px;
  color: #4a6cf7;
}

.dark-mode .file-icon-preview i {
  color: #7a9fff;
}

.file-icon-preview span {
  font-size: 14px;
  max-width: 200px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.cancel-file-button {
  position: absolute;
  right: 15px;
  top: 15px;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  cursor: pointer;
  font-size: 12px;
}

.cancel-file-button:hover {
  background-color: rgba(255, 0, 0, 0.7);
}

/* Message attachment styles */
.message-image-container {
  margin-bottom: 5px;
  position: relative;
  min-width: 100px;
  min-height: 100px;
  border-radius: 8px;
  overflow: hidden;
  background-color: #f5f5f5;
}

.dark-mode .message-image-container {
  background-color: #333;
}

.image-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.9rem;
  color: #666;
  padding: 10px;
  text-align: center;
}

.dark-mode .image-loading {
  color: #aaa;
}

.image-loading::before {
  content: "";
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid #ddd;
  border-top-color: #4a6cf7;
  border-radius: 50%;
  margin-right: 8px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.image-error {
  color: #d32f2f;
  background-color: #fff3f3;
  padding: 8px;
  border-radius: 4px;
  font-size: 0.85rem;
  text-align: center;
}

.dark-mode .image-error {
  background-color: #2c2121;
  color: #ff6b6b;
}

.message-image {
  max-width: 200px;
  max-height: 200px;
  border-radius: 8px;
  cursor: pointer;
  display: block;
}

.message-video-container {
  margin-bottom: 5px;
}

.message-video {
  max-width: 200px;
  max-height: 200px;
  border-radius: 8px;
}

.message-file-container {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 8px;
  margin-bottom: 5px;
  cursor: pointer;
}

.dark-mode .message.incoming .message-file-container {
  background-color: rgba(255, 255, 255, 0.1);
}

.message-file-container i {
  font-size: 18px;
  color: #4a6cf7;
}

.dark-mode .message-file-container i {
  color: #7a9fff;
}

.file-name {
  font-size: 13px;
  max-width: 150px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* GoFile.io styling */
.gofile-badge {
  position: absolute;
  bottom: 5px;
  right: 5px;
  background-color: rgba(74, 137, 220, 0.8);
  color: white;
  font-size: 0.7rem;
  padding: 2px 5px;
  border-radius: 3px;
  display: flex;
  align-items: center;
  gap: 3px;
  z-index: 2;
}

.dark-mode .gofile-badge {
  background-color: rgba(90, 153, 236, 0.8);
}

.gofile-label {
  font-size: 0.7rem;
  background-color: rgba(74, 137, 220, 0.8);
  color: white;
  padding: 2px 5px;
  border-radius: 3px;
  margin-left: 5px;
}

.dark-mode .gofile-label {
  background-color: rgba(90, 153, 236, 0.8);
}

/* Contact meta information */
.contact-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  margin-left: auto;
  min-width: 40px;
}

.contact-category-wrapper {
  margin-top: 10px;
}

.contact-time {
  font-size: 0.7rem;
  color: #888;
  margin-bottom: 4px;
}

.dark-mode .contact-time {
  color: #aaa;
}

.unread-count {
  background-color: #4a6cf7;
  color: white;
  font-size: 0.7rem;
  font-weight: 600;
  min-width: 18px;
  height: 18px;
  border-radius: 9px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 5px;
}

.dark-mode .unread-count {
  background-color: #5a77ff;
}

/* Confirm dialog */
.confirm-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.confirm-dialog {
  background-color: white;
  border-radius: 8px;
  width: 400px;
  max-width: 90%;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.dark-mode .confirm-dialog {
  background-color: #2d2d3a;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.confirm-dialog-header {
  padding: 20px;
  border-bottom: 1px solid #eaeaea;
}

.dark-mode .confirm-dialog-header {
  border-bottom: 1px solid #3a3a48;
}

.confirm-dialog-header h3 {
  margin: 0;
  font-size: 1.25rem;
  color: #333;
}

.dark-mode .confirm-dialog-header h3 {
  color: #e9e9e9;
}

.confirm-dialog-content {
  padding: 20px;
}

.confirm-dialog-content p {
  margin: 0;
  color: #555;
  line-height: 1.5;
}

.dark-mode .confirm-dialog-content p {
  color: #a7a7b3;
}

.confirm-dialog-actions {
  padding: 15px 20px;
  border-top: 1px solid #eaeaea;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.dark-mode .confirm-dialog-actions {
  border-top: 1px solid #3a3a48;
}

.btn-cancel,
.btn-danger {
  padding: 10px 20px;
  border-radius: 6px;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-cancel {
  background-color: #f0f0f0;
  color: #555;
}

.btn-cancel:hover {
  background-color: #e0e0e0;
}

.dark-mode .btn-cancel {
  background-color: #3a3a48;
  color: #e9e9e9;
}

.dark-mode .btn-cancel:hover {
  background-color: #464655;
}

.btn-danger {
  background-color: #dc3545;
  color: white;
}

.btn-danger:hover {
  background-color: #c82333;
}

.dark-mode .btn-danger {
  background-color: #e74c3c;
}

.dark-mode .btn-danger:hover {
  background-color: #c0392b;
}

/* File upload error styles */
.file-error {
  padding: 10px;
  margin: 5px 0;
  background-color: #fff3f3;
  border: 1px solid #fcc;
  border-radius: 6px;
  color: #d32f2f;
  font-size: 0.85rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.file-error::before {
  content: "\f071";
  font-family: "Font Awesome 5 Free";
  font-weight: 900;
  margin-right: 8px;
}

.video-error {
  padding: 15px;
  margin: 5px 0;
  background-color: #f8f9fa;
  border: 1px solid #ddd;
  border-radius: 6px;
  color: #555;
  font-size: 0.9rem;
  text-align: center;
}

/* Dark mode for error messages */
.dark-mode .file-error {
  background-color: #2c2121;
  border-color: #533535;
  color: #ff6b6b;
}

.dark-mode .video-error {
  background-color: #2a2a2a;
  border-color: #444;
  color: #ddd;
}

/* Extra small screens */
@media (max-width: 347px) {
  .input-container {
    padding: 0 1px;
  }

  .attachment-button,
  .emoji-button,
  .send-button {
    min-width: 28px;
    width: 28px;
    height: 28px;
    margin: 0;
  }

  .chat-input-area input {
    padding: 8px 5px;
    font-size: 0.9rem;
  }

  /* Remove extra space at bottom of chat */
  .chat-area {
    padding-bottom: 0;
    margin-bottom: 0;
  }

  .chat-input-area {
    padding: 8px 5px;
    margin-bottom: 0;
  }
}

/* Ensure all file preview elements have proper styling */
.message-image,
.message-video {
  max-width: 100%;
  max-height: 300px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.2s;
}

.message-image:hover,
.message-video:hover {
  transform: scale(1.02);
}

/* Style for file download link */
.message-file-container {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background-color: #f1f5f9;
  border-radius: 6px;
  margin: 5px 0;
  cursor: pointer;
  transition: background-color 0.2s;
}

.dark-mode .message-file-container {
  background-color: #323645;
}

.message-file-container:hover {
  background-color: #e3e9f0;
}

.dark-mode .message-file-container:hover {
  background-color: #3a3f52;
}

.message-file-container i {
  margin-right: 10px;
  color: #4b81e8;
}

.file-name {
  font-size: 0.9rem;
  color: #444;
  word-break: break-all;
}

.dark-mode .file-name {
  color: #ddd;
}

/* Search navigation */
.search-navigation {
  width: 90%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 0 auto;
  padding: 5px 0;
  opacity: 0;
  max-height: 0;
  overflow: hidden;
  transition: all 0.3s ease;
}

#search-navigation.active {
  opacity: 1;
  max-height: 40px;
  margin: 5px auto;
}

.search-count {
  font-size: 0.85rem;
  color: #666;
}

.dark-mode .search-count {
  color: #aaa;
}

.search-navigation-buttons {
  display: flex;
  gap: 5px;
}

.search-navigation-buttons button {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  border: none;
  background-color: #f0f0f0;
  color: #555;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.dark-mode .search-navigation-buttons button {
  background-color: #2a2a3a;
  color: #aaa;
}

.search-navigation-buttons button:hover:not(:disabled) {
  background-color: #e0e0e0;
  color: #4a6cf7;
}

.dark-mode .search-navigation-buttons button:hover:not(:disabled) {
  background-color: #3a3a4a;
  color: #7a9fff;
}

.search-navigation-buttons button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Search highlight */
.search-highlight {
  position: relative;
  animation: search-pulse 2s infinite;
  box-shadow: 0 0 10px rgba(74, 108, 247, 0.7);
  border: 2px solid rgba(74, 108, 247, 0.4);
  z-index: 1;
}

.current-highlight {
  box-shadow: 0 0 15px rgba(74, 108, 247, 1) !important;
  border: 2px solid rgba(74, 108, 247, 0.8) !important;
  animation: current-search-pulse 2s infinite !important;
  z-index: 2;
}

.dark-mode .search-highlight {
  box-shadow: 0 0 10px rgba(122, 159, 255, 0.7);
  border: 2px solid rgba(122, 159, 255, 0.4);
}

.dark-mode .current-highlight {
  box-shadow: 0 0 15px rgba(122, 159, 255, 1) !important;
  border: 2px solid rgba(122, 159, 255, 0.8) !important;
}

@keyframes search-pulse {
  0% {
    box-shadow: 0 0 10px rgba(74, 108, 247, 0.7);
  }
  50% {
    box-shadow: 0 0 15px rgba(74, 108, 247, 0.8);
  }
  100% {
    box-shadow: 0 0 10px rgba(74, 108, 247, 0.7);
  }
}

@keyframes current-search-pulse {
  0% {
    box-shadow: 0 0 15px rgba(74, 108, 247, 1);
  }
  50% {
    box-shadow: 0 0 20px rgba(74, 108, 247, 1);
  }
  100% {
    box-shadow: 0 0 15px rgba(74, 108, 247, 1);
  }
}

/* Dark mode animations are handled through the .dark-mode class selectors above */

/* Encryption badge */
.encrypted-badge {
  display: inline-block;
  margin-left: 5px;
  color: #4caf50;
  font-size: 12px;
  vertical-align: middle;
}

.dark-mode .encrypted-badge {
  color: #6fcf97;
}

/* Message urgency styles - only apply special styling to incoming messages */
.message.incoming.urgency-low {
  border-left: 3px solid #4caf50;
  background-color: rgba(76, 175, 80, 0.05);
}

.message.incoming.urgency-high {
  border-left: 3px solid #ff9800;
  background-color: rgba(255, 152, 0, 0.05);
}

.message.incoming.urgency-urgent {
  border-left: 3px solid #f44336;
  background-color: rgba(244, 67, 54, 0.05);
  animation: urgentPulse 2s infinite;
}

/* For outgoing messages, just add a small indicator but keep normal styling */
.message.outgoing.urgency-low .message-time .message-urgency,
.message.outgoing.urgency-high .message-time .message-urgency,
.message.outgoing.urgency-urgent .message-time .message-urgency {
  display: inline-block;
}

.dark-mode .message.urgency-low {
  background-color: rgba(76, 175, 80, 0.1);
}

.dark-mode .message.urgency-high {
  background-color: rgba(255, 152, 0, 0.1);
}

.dark-mode .message.urgency-urgent {
  background-color: rgba(244, 67, 54, 0.1);
}

/* Contact urgency indicators */
.contact-urgency-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  margin-left: 5px;
  font-size: 10px;
}

.contact-urgency-indicator.urgency-low {
  background-color: #4caf50;
  color: white;
}

.contact-urgency-indicator.urgency-high {
  background-color: #ff9800;
  color: white;
}

.contact-urgency-indicator.urgency-urgent {
  background-color: #f44336;
  color: white;
  animation: contactUrgentPulse 2s infinite;
}

/* Last message urgency prefix */
.last-message-content {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.urgency-prefix {
  font-weight: bold;
  margin-right: 2px;
}

.urgency-prefix.urgent {
  color: #f44336;
}

.urgency-prefix.high {
  color: #ff9800;
}

.urgency-prefix.low {
  color: #4caf50;
}

.dark-mode .urgency-prefix.urgent {
  color: #ff8a80;
}

.dark-mode .urgency-prefix.high {
  color: #ffcc80;
}

.dark-mode .urgency-prefix.low {
  color: #a5d6a7;
}

/* Contact item with urgent messages - only apply when the message is from the other person */
.contact-item:has(.contact-urgency-indicator.urgency-urgent) {
  background-color: rgba(244, 67, 54, 0.05);
  box-shadow: 0 0 5px rgba(244, 67, 54, 0.3);
}

.dark-mode .contact-item:has(.contact-urgency-indicator.urgency-urgent) {
  background-color: rgba(244, 67, 54, 0.1);
  box-shadow: 0 0 5px rgba(244, 67, 54, 0.5);
}

/* Contact item with high priority messages - only apply when the message is from the other person */
.contact-item:has(.contact-urgency-indicator.urgency-high) {
  background-color: rgba(255, 152, 0, 0.05);
}

.dark-mode .contact-item:has(.contact-urgency-indicator.urgency-high) {
  background-color: rgba(255, 152, 0, 0.1);
}

@keyframes contactUrgentPulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(244, 67, 54, 0.7);
  }
  70% {
    transform: scale(1.1);
    box-shadow: 0 0 0 10px rgba(244, 67, 54, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(244, 67, 54, 0);
  }
}

@keyframes urgentPulse {
  0% {
    border-color: #f44336;
    box-shadow: 0 0 5px rgba(244, 67, 54, 0.5);
  }
  50% {
    border-color: #ff8a80;
    box-shadow: 0 0 10px rgba(244, 67, 54, 0.8);
  }
  100% {
    border-color: #f44336;
    box-shadow: 0 0 5px rgba(244, 67, 54, 0.5);
  }
}

/* Emoji Picker Styles */
.emoji-picker-container {
  position: absolute;
  bottom: 60px;
  left: 10px;
  z-index: 100;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
  width: auto !important;
  height: auto !important;
  max-width: 350px !important;
  max-height: 435px !important;
  background: transparent !important;
}

/* Ensure emoji picker doesn't create a full-width overlay */
.emoji-picker-container > div {
  width: auto !important;
  background: transparent !important;
}

.emoji-picker-container > div > div {
  width: auto !important;
}

.dark-mode .emoji-picker-container {
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
}

/* Override emoji-picker-react styles for dark mode */
.dark-mode .emoji-picker-container .epr-body {
  background-color: #2d2d3a !important;
}

.dark-mode .emoji-picker-container .epr-search-container {
  background-color: #2d2d3a !important;
  border-color: #444 !important;
}

.dark-mode .emoji-picker-container .epr-search-container input {
  background-color: #35354a !important;
  border-color: #444 !important;
  color: #e0e0e0 !important;
}

.dark-mode .emoji-picker-container .epr-category-nav {
  background-color: #222230 !important;
  border-color: #444 !important;
}

.dark-mode .emoji-picker-container .epr-emoji-category-label {
  background-color: #2d2d3a !important;
  color: #a7a7b3 !important;
}

.dark-mode .emoji-picker-container .epr-preview {
  background-color: #222230 !important;
  border-color: #444 !important;
}

.dark-mode .emoji-picker-container .epr-preview .epr-preview-emoji-label {
  color: #e0e0e0 !important;
}

.dark-mode .emoji-picker-container .epr-emoji:hover {
  background-color: #35354a !important;
}

/* Additional dark mode overrides for emoji picker */
.dark-mode .emoji-picker-container .epr-header {
  background-color: #222230 !important;
  border-color: #444 !important;
}

.dark-mode .emoji-picker-container .epr-header .epr-header-overlay {
  background-color: #222230 !important;
}

.dark-mode .emoji-picker-container .epr-search {
  background-color: #35354a !important;
  border-color: #444 !important;
  color: #e0e0e0 !important;
}

.dark-mode .emoji-picker-container .epr-emoji-category {
  background-color: #2d2d3a !important;
}

.dark-mode .emoji-picker-container .epr-body .epr-emoji-list {
  background-color: #2d2d3a !important;
}

.dark-mode .emoji-picker-container .epr-body::-webkit-scrollbar-track {
  background-color: #2d2d3a !important;
}

.dark-mode .emoji-picker-container .epr-body::-webkit-scrollbar-thumb {
  background-color: #444 !important;
}

.dark-mode .emoji-picker-container .epr-skin-tones {
  background-color: #222230 !important;
}

.dark-mode .emoji-picker-container .epr-frequently-used {
  background-color: #2d2d3a !important;
  color: #a7a7b3 !important;
}

.dark-mode .emoji-picker-container .epr-search-container .epr-icn-search {
  color: #a7a7b3 !important;
}

.dark-mode .emoji-picker-container .epr-category-nav .epr-active-category {
  background-color: #35354a !important;
  border-color: #444 !important;
}

.dark-mode .emoji-picker-container .epr-category-nav button {
  color: #a7a7b3 !important;
}

.dark-mode .emoji-picker-container .epr-category-nav button:hover {
  background-color: #35354a !important;
}

.dark-mode .emoji-picker-container .epr-search-container .epr-icn-search svg {
  fill: #a7a7b3 !important;
}

/* Additional fixes for any remaining white elements */
.dark-mode .emoji-picker-container * {
  border-color: #444 !important;
}

/* Specific dark background for emoji picker elements */
.dark-mode .emoji-picker-container .epr-body,
.dark-mode .emoji-picker-container .epr-search-container,
.dark-mode .emoji-picker-container .epr-header,
.dark-mode .emoji-picker-container .epr-category-nav,
.dark-mode .emoji-picker-container .epr-emoji-category,
.dark-mode .emoji-picker-container .epr-preview {
  background-color: #2d2d3a !important;
  color: #e0e0e0 !important;
}

/* Fix search input styling in dark mode */
.dark-mode .emoji-picker-container .epr-search-container input {
  background-color: #35354a !important;
  color: #e0e0e0 !important;
  border-color: #444 !important;
}

.dark-mode .emoji-picker-container .epr-emoji-list > div {
  background-color: #2d2d3a !important;
}

.dark-mode .emoji-picker-container .epr-emoji-list > div > div {
  background-color: #2d2d3a !important;
}

.dark-mode .emoji-picker-container .epr-emoji-list > div > div > div {
  background-color: #2d2d3a !important;
}

.dark-mode .emoji-picker-container .epr-emoji-list > div > div > div > div {
  background-color: #2d2d3a !important;
}

.dark-mode
  .emoji-picker-container
  .epr-emoji-list
  > div
  > div
  > div
  > div
  > div {
  background-color: #2d2d3a !important;
}

.dark-mode
  .emoji-picker-container
  .epr-emoji-list
  > div
  > div
  > div
  > div
  > div
  > div {
  background-color: #2d2d3a !important;
}

.dark-mode
  .emoji-picker-container
  .epr-emoji-list
  > div
  > div
  > div
  > div
  > div
  > div
  > div {
  background-color: #2d2d3a !important;
}

.dark-mode
  .emoji-picker-container
  .epr-emoji-list
  > div
  > div
  > div
  > div
  > div
  > div
  > div
  > div {
  background-color: #2d2d3a !important;
}

.dark-mode
  .emoji-picker-container
  .epr-emoji-list
  > div
  > div
  > div
  > div
  > div
  > div
  > div
  > div
  > div {
  background-color: #2d2d3a !important;
}

.dark-mode
  .emoji-picker-container
  .epr-emoji-list
  > div
  > div
  > div
  > div
  > div
  > div
  > div
  > div
  > div
  > div {
  background-color: #2d2d3a !important;
}

.emoji-button,
.attachment-button,
.google-drive-button {
  color: #4a89dc;
  font-size: 1.1rem;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: transparent;
}

.dark-mode .emoji-button,
.dark-mode .attachment-button,
.dark-mode .google-drive-button {
  color: #5a99ec;
}

.emoji-button:hover,
.attachment-button:hover {
  background-color: rgba(74, 137, 220, 0.1);
  color: #3a79cc;
  transform: translateY(-2px);
}

.dark-mode .emoji-button:hover,
.dark-mode .attachment-button:hover {
  background-color: rgba(90, 153, 236, 0.15);
  color: #7ab0ff;
}

.google-drive-button {
  color: #4285f4;
}

.dark-mode .google-drive-button {
  color: #5a99ec;
}

.google-drive-button:hover {
  background-color: rgba(66, 133, 244, 0.1);
  color: #0f9d58;
  transform: translateY(-2px);
}

.dark-mode .google-drive-button:hover {
  background-color: rgba(66, 133, 244, 0.15);
  color: #34a853;
}

/* Modal overlay for Google Drive */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}
