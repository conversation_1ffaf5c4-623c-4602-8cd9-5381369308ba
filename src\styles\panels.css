.panels-container {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
}

.panel {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: space-around;
  text-align: center;
  z-index: 6;
}

.left-panel::before {
  width: 100%;
  pointer-events: all;
  padding: 3rem 17% 2rem 12%;
}

.right-panel {
  z-index: 10 !important;
  pointer-events: none;
  padding: 3rem 12% 2rem 17%;
}

.panel .content {
  color: #fff;
  transition: 0.9s 0.6s ease-in-out;
}

.panel h3 {
  font-weight: 600;
  line-height: 1;
  font-size: 1.5rem;
}

.panel p {
  font-size: 0.95rem;
  padding: 0.7rem 0;
}

.image {
  width: 100%;
  transition: 1.1s 0.4s ease-in-out;
}

.right-panel .content,
.right-panel .image {
  transform: translateX(800px);
}

.login-side-container,
.signup-side-container {
  transition: transform 0.6s ease-in-out, opacity 0.6s ease-in-out;
}

/* Sign-up Mode */
.sign-up-mode .left-panel {
  pointer-events: none;
}

.sign-up-mode .right-panel {
  pointer-events: all;
}

.sign-up-mode .left-panel .image,
.sign-up-mode .left-panel .content {
  transform: translateX(-800px);
}

.sign-up-mode .right-panel .content,
.sign-up-mode .right-panel .image {
  transform: translateX(0);
}

.sign-up-mode .left-panel {
  z-index: 7;
}

.sign-up-mode .right-panel {
  z-index: 2;
}