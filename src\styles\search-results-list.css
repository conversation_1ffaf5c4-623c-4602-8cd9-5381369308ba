/* Search results list */
.search-results-list {
  width: 90%;
  max-height: 0;
  overflow: hidden;
  margin: 0 auto;
  background-color: #f5f5f5;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  opacity: 0;
}

.dark-mode .search-results-list {
  background-color: #35354a;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.search-results-list.active {
  max-height: 200px;
  opacity: 1;
  margin: 8px auto;
  overflow-y: auto;
  animation: fadeIn 0.3s ease;
}

.search-result-item {
  padding: 10px 12px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  flex-direction: column;
}

.dark-mode .search-result-item {
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.search-result-item:last-child {
  border-bottom: none;
}

.search-result-item:hover {
  background-color: rgba(74, 108, 247, 0.1);
}

.dark-mode .search-result-item:hover {
  background-color: rgba(90, 119, 255, 0.15);
}

.search-result-text {
  font-size: 0.9rem;
  color: #333;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dark-mode .search-result-text {
  color: #e0e0e0;
}

.search-result-text .highlight {
  background-color: rgba(74, 108, 247, 0.2);
  padding: 0 2px;
  border-radius: 2px;
}

.dark-mode .search-result-text .highlight {
  background-color: rgba(90, 119, 255, 0.3);
}

.search-result-meta {
  display: flex;
  justify-content: space-between;
  font-size: 0.75rem;
  color: #777;
}

.dark-mode .search-result-meta {
  color: #a7a7b3;
}

.search-result-sender {
  font-weight: 500;
}

.search-result-time {
  font-style: italic;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-5px); }
  to { opacity: 1; transform: translateY(0); }
}
