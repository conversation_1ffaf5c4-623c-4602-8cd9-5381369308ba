/* Styles pour le composant SearchBar */
.search-bar-wrapper {
  width: 100%;
  max-width: 400px;
}

.search-bar {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border: 1px solid transparent;
  border-radius: 20px;
  padding: 12px 18px;
  transition: all 0.3s ease;
  position: relative;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.search-bar:focus-within {
  border-color: rgba(68, 129, 235, 0.2);
  box-shadow: 0 4px 12px rgba(68, 129, 235, 0.15);
  transform: translateY(-1px);
}

.dark-mode .search-bar {
  background-color: #35354a;
  border-color: transparent;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.dark-mode .search-bar:focus-within {
  border-color: rgba(4, 190, 254, 0.3);
  box-shadow: 0 4px 12px rgba(4, 190, 254, 0.2);
}

.search-bar i {
  color: #6c757d;
  margin-right: 12px;
  font-size: 0.9rem;
}

.dark-mode .search-bar i {
  color: #a7a7b3;
}

.search-bar input {
  flex: 1;
  border: none;
  background: transparent;
  outline: none;
  color: #333;
  font-size: 0.95rem;
  width: 100%;
  padding: 4px 0;
}

.dark-mode .search-bar input {
  color: #e9e9e9;
}

.search-bar input::placeholder {
  color: #adb5bd;
}

.dark-mode .search-bar input::placeholder {
  color: #6c7293;
}

.search-bar .clear-button {
  background: none;
  border: none;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8px;
  cursor: pointer;
  color: #6c757d;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.dark-mode .search-bar .clear-button {
  color: #a7a7b3;
}

.search-bar .clear-button:hover {
  color: #4a6cf7;
  transform: rotate(90deg);
  background-color: rgba(74, 108, 247, 0.1);
}

.dark-mode .search-bar .clear-button:hover {
  color: #7a9fff;
  background-color: rgba(122, 159, 255, 0.1);
}

/* Responsive styles */
@media (max-width: 768px) {
  .search-bar-wrapper {
    max-width: 100%;
  }

  .search-bar {
    padding: 10px 14px;
  }
}

/* Animation pour le bouton clear */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.search-bar .clear-button {
  animation: fadeIn 0.2s ease-out;
}
