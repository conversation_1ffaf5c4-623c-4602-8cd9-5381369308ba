.security-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  height: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.dark-mode .security-container {
  color: #e9e9e9;
}

.security-header {
  margin-bottom: 30px;
}

.security-header h1 {
  font-size: 2rem;
  margin-bottom: 10px;
  color: #333;
}

.dark-mode .security-header h1 {
  color: #e9e9e9;
}

.security-header p {
  color: #6c757d;
  font-size: 1rem;
}

.dark-mode .security-header p {
  color: #a7a7b3;
}

.security-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  margin-bottom: 30px;
  overflow: hidden;
}

.dark-mode .security-card {
  background-color: #2d2d3a;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.security-card-header {
  padding: 20px;
  border-bottom: 1px solid #eaeaea;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dark-mode .security-card-header {
  border-bottom: 1px solid #3a3a48;
}

.security-card-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: #333;
  display: flex;
  align-items: center;
}

.dark-mode .security-card-title {
  color: #e9e9e9;
}

.security-card-title i {
  margin-right: 10px;
  color: #4a6cf7;
}

.dark-mode .security-card-title i {
  color: #5a77ff;
}

.security-card-content {
  padding: 20px;
}

/* Form Styles */
.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #495057;
}

.dark-mode .form-group label {
  color: #e9e9e9;
}

.form-group input[type="password"],
.form-group input[type="text"] {
  width: 100%;
  padding: 10px 15px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 1rem;
  color: #495057;
  background-color: #fff;
}

.dark-mode .form-group input[type="password"],
.dark-mode .form-group input[type="text"] {
  border-color: #3a3a48;
  background-color: #35354a;
  color: #e9e9e9;
}

.form-group input:focus {
  outline: none;
  border-color: #4a6cf7;
  box-shadow: 0 0 0 3px rgba(74, 108, 247, 0.2);
}

.dark-mode .form-group input:focus {
  border-color: #5a77ff;
  box-shadow: 0 0 0 3px rgba(90, 119, 255, 0.2);
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

.btn {
  padding: 10px 20px;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
}

.btn-primary {
  background-color: #4a6cf7;
  color: #fff;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  max-width: 200px;
  margin: auto;
}

.btn-primary:hover {
  background-color: #385de0;
}

.dark-mode .btn-primary {
  background-color: #5a77ff;
}

.dark-mode .btn-primary:hover {
  background-color: #4a66e0;
}

/* Password Strength */
.strength-bar-container {
  height: 6px;
  background-color: #e0e0e0;
  border-radius: 3px;
  margin-top: 10px;
  margin-bottom: 5px;
  width: 100%;
}

.dark-mode .strength-bar-container {
  background-color: #444;
}

.strength-bar {
  height: 100%;
  border-radius: 3px;
  transition: width 0.3s ease, background-color 0.3s ease;
}

.strength-text {
  font-size: 0.8rem;
  text-align: right;
  font-weight: 500;
}

/* Success and Error Messages */
.success-message,
.error-message {
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}

.success-message {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.error-message {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.success-message i,
.error-message i {
  margin-right: 10px;
  font-size: 1.1rem;
}

/* Info Text */
.security-info-text {
  color: #6c757d;
  line-height: 1.6;
}

.dark-mode .security-info-text {
  color: #a7a7b3;
}

/* Media Queries */
@media (max-width: 768px) {
  .security-container {
    padding: 15px;
  }

  .security-card {
    margin-bottom: 20px;
  }

  .security-card-header,
  .security-card-content {
    padding: 15px;
  }

  .security-header h1 {
    font-size: 1.5rem;
  }

  .btn {
    width: 100%;
  }
}
