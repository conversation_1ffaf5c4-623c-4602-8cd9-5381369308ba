.settings-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  height: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.settings-header h1 {
  font-size: 1.8rem;
  margin: 0;
  color: #333;
}

.dark-mode .settings-header h1 {
  color: #f1f1f1;
}

.settings-actions {
  display: flex;
  gap: 10px;
}

/* Alerts */
.alert {
  padding: 12px 15px;
  border-radius: 8px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.alert i {
  font-size: 1.1rem;
}

.alert-success {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.dark-mode .alert-success {
  background-color: #1e3320;
  color: #81c784;
}

.alert-danger {
  background-color: #ffebee;
  color: #d32f2f;
}

.dark-mode .alert-danger {
  background-color: #3e2929;
  color: #f48fb1;
}

/* Settings Grid */
.settings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

/* Settings Card */
.settings-card {
  background-color: white;
  border-radius: 20px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  transition: all 0.3s ease;
}

.settings-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.08);
}

.dark-mode .settings-card {
  background-color: #2d2d2d;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.dark-mode .settings-card:hover {
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.25);
}

.settings-card-header {
  padding: 15px 20px;
  border-bottom: 1px solid #eee;
}

.dark-mode .settings-card-header {
  border-bottom-color: #444;
}

.settings-card-header h2 {
  margin: 0;
  font-size: 1.3rem;
  color: #333;
}

.dark-mode .settings-card-header h2 {
  color: #f1f1f1;
}

.settings-card-body {
  padding: 20px;
}

/* Setting Group */
.setting-group {
  margin-bottom: 25px;
}

.setting-group:last-child {
  margin-bottom: 0;
}

.setting-group h3 {
  font-size: 1.1rem;
  margin: 0 0 15px 0;
  color: #444;
}

.dark-mode .setting-group h3 {
  color: #ddd;
}

/* Modern Theme Options */
.theme-options-modern {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 20px;
}

.theme-option-modern {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  border-radius: 12px;
  border: 2px solid #e0e0e0;
  background-color: #fff;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.dark-mode .theme-option-modern {
  background-color: #2d2d3a;
  border-color: #3a3a48;
}

.theme-option-modern:hover {
  border-color: #4481eb;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(68, 129, 235, 0.15);
}

.theme-option-modern.active {
  border-color: #4481eb;
  background-color: #f8faff;
  box-shadow: 0 8px 25px rgba(68, 129, 235, 0.2);
}

.dark-mode .theme-option-modern.active {
  background-color: #2a2a3a;
  border-color: #5a77ff;
  box-shadow: 0 8px 25px rgba(90, 119, 255, 0.2);
}

.theme-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  font-size: 20px;
  transition: all 0.3s ease;
}

.theme-option-modern:nth-child(1) .theme-icon {
  background-color: #fff3cd;
  color: #f39c12;
}

.theme-option-modern:nth-child(2) .theme-icon {
  background-color: #d1ecf1;
  color: #17a2b8;
}

.theme-option-modern:nth-child(3) .theme-icon {
  background-color: #e2e3e5;
  color: #6c757d;
}

.dark-mode .theme-option-modern:nth-child(1) .theme-icon {
  background-color: rgba(243, 156, 18, 0.2);
  color: #f39c12;
}

.dark-mode .theme-option-modern:nth-child(2) .theme-icon {
  background-color: rgba(23, 162, 184, 0.2);
  color: #17a2b8;
}

.dark-mode .theme-option-modern:nth-child(3) .theme-icon {
  background-color: rgba(108, 117, 125, 0.2);
  color: #adb5bd;
}

.theme-content {
  flex: 1;
}

.theme-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.dark-mode .theme-title {
  color: #f1f1f1;
}

.theme-subtitle {
  font-size: 14px;
  color: #666;
}

.dark-mode .theme-subtitle {
  color: #aaa;
}

.theme-check {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #4481eb;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  animation: checkmark-bounce 0.3s ease;
}

.dark-mode .theme-check {
  background-color: #5a77ff;
}

@keyframes checkmark-bounce {
  0% {
    transform: scale(0);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

.current-theme {
  padding: 12px 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
  font-size: 14px;
  color: #666;
}

.dark-mode .current-theme {
  background-color: #3a3a48;
  color: #aaa;
}

.current-theme-value {
  font-weight: 600;
  color: #4481eb;
}

.dark-mode .current-theme-value {
  color: #5a77ff;
}

.setting-description {
  font-size: 14px;
  color: #666;
  margin-bottom: 16px;
  line-height: 1.5;
}

.dark-mode .setting-description {
  color: #aaa;
}

/* Legacy Theme Options (keeping for backward compatibility) */
.theme-options {
  display: flex;
  gap: 15px;
}

.theme-option {
  border-radius: 8px;
  padding: 10px;
  cursor: pointer;
  width: 100px;
  text-align: center;
  border: 2px solid transparent;
  transition: border-color 0.2s;
}

.theme-option.active {
  border-color: #4481eb;
  box-shadow: 0 5px 15px rgba(68, 129, 235, 0.2);
  transform: translateY(-2px);
}

.theme-preview {
  height: 60px;
  border-radius: 6px;
  margin-bottom: 8px;
}

.light-theme {
  background-color: #f5f5f5;
  border: 1px solid #ddd;
}

.dark-theme {
  background-color: #333;
  border: 1px solid #555;
}

.theme-label {
  color: #555;
  font-weight: 500;
}

.dark-mode .theme-label {
  color: #ddd;
}

/* Font Size Options */
.font-size-options {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
}

.font-size-option {
  background-color: #f5f5f5;
  border: 2px solid transparent;
  border-radius: 8px;
  padding: 12px;
  cursor: pointer;
  text-align: center;
  width: 80px;
  transition: all 0.2s ease;
}

.dark-mode .font-size-option {
  background-color: #3a3a3a;
}

.font-size-option:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.font-size-option.active {
  border-color: #4481eb;
  box-shadow: 0 5px 15px rgba(68, 129, 235, 0.2);
  transform: translateY(-2px);
}

.font-size-preview {
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
}

.dark-mode .font-size-preview {
  color: #f1f1f1;
}

.font-size-preview.small {
  font-size: 16px;
}

.font-size-preview.medium {
  font-size: 20px;
}

.font-size-preview.large {
  font-size: 24px;
}

.font-size-label {
  font-size: 0.9rem;
  color: #555;
}

.dark-mode .font-size-label {
  color: #ddd;
}

/* Avatar Color Options */
.avatar-color-options {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 20px;
}

.avatar-color-option {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  border: 2px solid transparent;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-color-option:hover {
  transform: scale(1.1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.avatar-color-option.active {
  border-color: #fff;
  box-shadow: 0 0 0 2px #4a6cf7;
}

.avatar-color-option i {
  color: white;
  font-size: 16px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Custom Color Picker */
.custom-color-option {
  margin: 20px 0;
  position: relative;
}

.custom-color-button {
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 10px 15px;
  cursor: pointer;
  font-size: 0.9rem;
  color: #444;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s ease;
}

.custom-color-button:hover {
  background-color: #eee;
  transform: translateY(-2px);
}

.dark-mode .custom-color-button {
  background-color: #3a3a3a;
  border-color: #555;
  color: #ddd;
}

.dark-mode .custom-color-button:hover {
  background-color: #444;
}

.color-picker-container {
  position: absolute;
  z-index: 10;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  overflow: hidden;
  left: 0;
  bottom: 100%; /* Position above the button instead of below */
  margin-bottom: 10px; /* Add margin to the bottom instead of top */
  background-color: white;
}

.dark-mode .color-picker-container {
  background-color: #333;
}

.color-picker-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background-color: #f5f5f5;
  border-bottom: 1px solid #ddd;
}

.dark-mode .color-picker-header {
  background-color: #444;
  border-bottom-color: #555;
  color: #ddd;
}

.color-picker-close {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 16px;
  color: #555;
  padding: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  transition: all 0.2s ease;
}

.color-picker-close:hover {
  background-color: #eee;
  color: #333;
}

.dark-mode .color-picker-close {
  color: #ddd;
}

.dark-mode .color-picker-close:hover {
  background-color: #555;
  color: #fff;
}

.avatar-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 20px;
}

.user-avatar-preview {
  width: 65px;
  height: 65px;
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 26px;
  font-weight: bold;
  color: white;
  margin-bottom: 12px;
  box-shadow: 0 6px 18px rgba(0, 0, 0, 0.25);
  transition: all 0.3s ease;
}

.user-avatar-preview:hover {
  transform: scale(1.05);
  box-shadow: 0 8px 22px rgba(0, 0, 0, 0.35);
}

.avatar-preview-label {
  font-size: 0.9rem;
  color: #555;
}

.dark-mode .avatar-preview-label {
  color: #ddd;
}

/* Language Select */
.language-select {
  width: 100%;
  padding: 10px 15px;
  border-radius: 5px;
  border: 1px solid #ddd;
  font-size: 1rem;
  color: #333;
  background-color: white;
}

.dark-mode .language-select {
  background-color: #333;
  border-color: #555;
  color: #f0f0f0;
}

/* Profile Fields */
.profile-field {
  margin-bottom: 15px;
}

.profile-field label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #555;
}

.dark-mode .profile-field label {
  color: #ddd;
}

.profile-field input,
.profile-field textarea {
  width: 100%;
  padding: 10px 15px;
  border-radius: 5px;
  border: 1px solid #ddd;
  font-size: 1rem;
  color: #333;
  background-color: white;
  transition: border-color 0.2s;
}

.dark-mode .profile-field input,
.dark-mode .profile-field textarea {
  background-color: #333;
  border-color: #555;
  color: #f0f0f0;
}

.profile-field input:focus,
.profile-field textarea:focus {
  outline: none;
  border-color: #4a6cf7;
}

.profile-field input:disabled {
  background-color: #f9f9f9;
  color: #888;
  cursor: not-allowed;
}

.dark-mode .profile-field input:disabled {
  background-color: #2a2a2a;
  color: #777;
}

.field-info {
  margin-top: 5px;
  color: #888;
  font-size: 0.85rem;
}

.dark-mode .field-info {
  color: #777;
}

/* Notification Options */
.notification-option,
.privacy-option {
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
}

.dark-mode .notification-option,
.dark-mode .privacy-option {
  border-bottom-color: #444;
}

.notification-option:last-child,
.privacy-option:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.notification-option-label,
.privacy-option-label {
  font-weight: 500;
  margin-bottom: 5px;
  color: #444;
}

.dark-mode .notification-option-label,
.dark-mode .privacy-option-label {
  color: #ddd;
}

.notification-option-info,
.privacy-option-info {
  color: #777;
  margin-bottom: 10px;
  font-size: 0.9rem;
}

.dark-mode .notification-option-info,
.dark-mode .privacy-option-info {
  color: #aaa;
}

/* Switch Toggle */
.switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 26px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.4s;
  border-radius: 34px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: 0.4s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: #4a6cf7;
}

.dark-mode input:checked + .slider {
  background-color: #5a77ff;
}

input:focus + .slider {
  box-shadow: 0 0 1px #4a6cf7;
}

.dark-mode input:focus + .slider {
  box-shadow: 0 0 1px #5a77ff;
}

input:checked + .slider:before {
  transform: translateX(24px);
}

/* Auto Reply */
.auto-reply-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.auto-reply-label {
  font-weight: 500;
  color: #444;
}

.dark-mode .auto-reply-label {
  color: #ddd;
}

.auto-reply-message textarea {
  width: 100%;
  padding: 10px 15px;
  border-radius: 5px;
  border: 1px solid #ddd;
  font-size: 1rem;
  color: #333;
  resize: vertical;
  background-color: white;
  transition: border-color 0.2s;
}

.dark-mode .auto-reply-message textarea {
  background-color: #333;
  border-color: #555;
  color: #f0f0f0;
}

.auto-reply-message textarea:focus {
  outline: none;
  border-color: #4a6cf7;
}

.auto-reply-message textarea:disabled {
  background-color: #f9f9f9;
  color: #888;
  cursor: not-allowed;
}

.dark-mode .auto-reply-message textarea:disabled {
  background-color: #2a2a2a;
  color: #777;
}

/* Button Styles */
.btn {
  padding: 10px 20px;
  border-radius: 5px;
  font-weight: 500;
  cursor: pointer;
  font-size: 1rem;
  border: none;
  transition: background-color 0.2s, transform 0.2s;
}

.btn:hover {
  transform: translateY(-2px);
}

.btn-primary {
  background-color: #4a6cf7;
  color: white;
}

.btn-primary:hover {
  background-color: #3959d9;
}

.btn-primary:disabled {
  background-color: #a0aee9;
  cursor: not-allowed;
  transform: none;
}

.btn-secondary {
  background-color: #f5f5f5;
  color: #555;
  border: 1px solid #ddd;
}

.dark-mode .btn-secondary {
  background-color: #3a3a3a;
  border-color: #555;
  color: #ddd;
}

.btn-secondary:hover {
  background-color: #eee;
}

.dark-mode .btn-secondary:hover {
  background-color: #444;
}

/* Loading Message */
.loading-message {
  text-align: center;
  padding: 50px;
  color: #777;
  font-size: 1.1rem;
}

.dark-mode .loading-message {
  color: #aaa;
}

/* Responsive Design */
@media (max-width: 768px) {
  .settings-grid {
    grid-template-columns: 1fr;
  }

  .settings-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .theme-options {
    flex-direction: column;
    align-items: center;
  }

  .theme-option {
    width: 100%;
    max-width: 150px;
  }

  .settings-row {
    flex-direction: column;
    align-items: flex-start;
  }

  .avatar-preview {
    margin-left: 0;
    margin-top: 20px;
    align-self: center;
  }

  .color-picker-container {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1000;
    bottom: auto;
    margin-bottom: 0;
    max-width: 90%;
    max-height: 90vh;
    overflow-y: auto;
  }

  /* Add a backdrop for mobile */
  body.color-picker-open::before {
    content: "";
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
  }
}

/* Sidebar Hover Setting Option */
.setting-option {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 10px;
}

.setting-label {
  font-size: 0.95rem;
  color: #444;
}

.dark-mode .setting-label {
  color: #ddd;
}

.setting-description {
  margin-top: 10px;
  font-size: 0.85rem;
  color: #777;
  line-height: 1.5;
}

.dark-mode .setting-description {
  color: #aaa;
}
