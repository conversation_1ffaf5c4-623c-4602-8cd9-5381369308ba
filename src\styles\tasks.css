.task-management {
  padding: 15px;
  height: 100%;
  overflow-y: auto;
  background-color: #fff;
}

.dark-mode .task-management {
  background-color: #222230;
  color: #e9e9e9;
}

.task-header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.add-task-btn {
  background-color: #4caf50;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
}

.add-task-btn:hover {
  background-color: #45a049;
}

.add-task-btn:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.task-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 15px;
}

.task-item {
  background-color: #ffffff;
  border-radius: 18px;
  padding: 24px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05), 0 5px 10px rgba(0, 0, 0, 0.02);
  transition: all 0.3s ease;
  border-left: 5px solid #ccc;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(230, 230, 230, 0.7);
}

.task-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.2) 0%,
    rgba(255, 255, 255, 0) 50%
  );
  pointer-events: none;
}

.dark-mode .task-item {
  background-color: #2a2a38;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2), 0 5px 10px rgba(0, 0, 0, 0.1);
  border-color: #444;
  border: 1px solid rgba(60, 60, 70, 0.7);
}

.dark-mode .task-item::before {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.05) 0%,
    rgba(255, 255, 255, 0) 50%
  );
}

.task-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.08), 0 8px 15px rgba(0, 0, 0, 0.03);
}

.task-item.status-todo {
  border-left-color: #ff9800;
}

.task-item.status-doing {
  border-left-color: #2196f3;
}

.task-item.status-done {
  border-left-color: #4caf50;
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.task-title {
  margin: 0;
  font-size: 18px;
  cursor: pointer;
  color: #333;
  font-weight: 600;
  transition: color 0.2s ease;
  letter-spacing: 0.2px;
}

.dark-mode .task-title {
  color: #e9e9e9;
}

.task-title:hover {
  color: #4a6cf7;
}

.dark-mode .task-title:hover {
  color: #7a9fff;
}

.task-status {
  font-size: 12px;
  padding: 7px 14px;
  border-radius: 30px;
  font-weight: 600;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  letter-spacing: 0.3px;
}

.dark-mode .task-status {
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.task-status.status-todo {
  background-color: #fff3e0;
  color: #e65100;
  border: 1px solid rgba(230, 81, 0, 0.1);
}

.dark-mode .task-status.status-todo {
  background-color: rgba(255, 152, 0, 0.2);
  color: #ffb74d;
}

.task-status.status-doing {
  background-color: #e3f2fd;
  color: #0d47a1;
  border: 1px solid rgba(13, 71, 161, 0.1);
}

.dark-mode .task-status.status-doing {
  background-color: rgba(33, 150, 243, 0.2);
  color: #64b5f6;
}

.task-status.status-done {
  background-color: #e8f5e9;
  color: #1b5e20;
  border: 1px solid rgba(27, 94, 32, 0.1);
}

.dark-mode .task-status.status-done {
  background-color: rgba(76, 175, 80, 0.2);
  color: #81c784;
}

.task-status.status-overdue {
  background-color: #ffebee;
  color: #c62828;
  border: 1px solid rgba(198, 40, 40, 0.1);
}

.dark-mode .task-status.status-overdue {
  background-color: rgba(244, 67, 54, 0.2);
  color: #e57373;
}

.task-item.overdue {
  border-color: #f44336;
  box-shadow: 0 5px 15px rgba(244, 67, 54, 0.25);
  animation: pulse-red 2s infinite;
}

.dark-mode .task-item.overdue {
  border-color: #e57373;
  box-shadow: 0 3px 10px rgba(244, 67, 54, 0.25);
}

@keyframes pulse-red {
  0% {
    box-shadow: 0 3px 10px rgba(244, 67, 54, 0.2);
  }
  50% {
    box-shadow: 0 3px 15px rgba(244, 67, 54, 0.4);
  }
  100% {
    box-shadow: 0 3px 10px rgba(244, 67, 54, 0.2);
  }
}

.task-info {
  margin-bottom: 18px;
  font-size: 14px;
  line-height: 1.5;
  color: #555;
}

.dark-mode .task-info {
  color: #aaa;
}

.task-status-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.task-checkbox {
  width: 18px;
  height: 18px;
  accent-color: #4caf50;
  cursor: pointer;
  transition: all 0.2s ease;
  margin: 0;
}

.task-info p {
  margin: 8px 0;
  display: flex;
  align-items: center;
  gap: 6px;
}

.task-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 5px;
}

.edit-btn,
.delete-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 30px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

.edit-btn {
  background-color: #2196f3;
  color: white;
}

.edit-btn:hover {
  background-color: #0b7dda;
}

.edit-btn.disabled {
  background-color: #a0a0a0;
  cursor: not-allowed;
  opacity: 0.7;
}

.edit-btn.disabled:hover {
  background-color: #a0a0a0;
  transform: none;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.delete-btn {
  background-color: #f44336;
  color: white;
  box-shadow: 0 2px 5px rgba(244, 67, 54, 0.2);
}

.delete-btn:hover {
  background-color: #d32f2f;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(244, 67, 54, 0.3);
}

.task-loading,
.task-error,
.no-tasks {
  text-align: center;
  padding: 20px;
  color: #666;
}

.task-error {
  color: #f44336;
}

/* Task Details Modal */
.task-details-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.task-details-content {
  background-color: white;
  border-radius: 20px;
  padding: 30px;
  width: 90%;
  max-width: 600px;
  max-height: 85vh;
  overflow-y: auto;
  position: relative;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
  animation: slideUp 0.3s ease-out;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.dark-mode .task-details-content {
  background-color: #2d2d3a;
  color: #e9e9e9;
  border: 1px solid rgba(255, 255, 255, 0.05);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
}

@keyframes slideUp {
  from {
    transform: translateY(30px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.close-btn {
  position: absolute;
  top: 15px;
  right: 20px;
  font-size: 24px;
  background: none;
  border: none;
  cursor: pointer;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background-color: rgba(0, 0, 0, 0.05);
  color: #333;
  transform: rotate(90deg);
}

.dark-mode .close-btn {
  color: #aaa;
}

.dark-mode .close-btn:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: #fff;
}

.task-detail-info {
  margin: 25px 0;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.task-status-badge {
  display: inline-flex;
  align-items: center;
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 14px;
  margin-top: 10px;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
}

.dark-mode .task-status-badge {
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
}

.task-status-badge.status-todo {
  background-color: #fff3e0;
  color: #e65100;
}

.dark-mode .task-status-badge.status-todo {
  background-color: rgba(255, 152, 0, 0.2);
  color: #ffb74d;
}

.task-status-badge.status-doing {
  background-color: #e3f2fd;
  color: #0d47a1;
}

.dark-mode .task-status-badge.status-doing {
  background-color: rgba(33, 150, 243, 0.2);
  color: #64b5f6;
}

.task-status-badge.status-done {
  background-color: #e8f5e9;
  color: #1b5e20;
}

.dark-mode .task-status-badge.status-done {
  background-color: rgba(76, 175, 80, 0.2);
  color: #81c784;
}

.detail-row {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  padding: 12px 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.dark-mode .detail-row {
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(74, 108, 247, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #4a6cf7;
  flex-shrink: 0;
}

.dark-mode .detail-icon {
  background-color: rgba(90, 119, 255, 0.15);
  color: #5a77ff;
}

.detail-content {
  flex: 1;
}

.detail-content h4 {
  margin: 0 0 5px 0;
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.dark-mode .detail-content h4 {
  color: #aaa;
}

.detail-content p {
  margin: 0;
  font-size: 16px;
  color: #333;
}

.dark-mode .detail-content p {
  color: #e9e9e9;
}

.status-update {
  margin: 30px 0;
  padding: 20px;
  background-color: #f5f5f5;
  border-radius: 15px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.dark-mode .status-update {
  background-color: #35354a;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.status-update h3 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 18px;
  color: #333;
}

.dark-mode .status-update h3 {
  color: #e9e9e9;
}

.status-buttons {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

.status-toggle {
  margin-top: 15px;
}

.status-toggle-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  background-color: white;
  padding: 12px 20px;
  border-radius: 12px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.dark-mode .status-toggle-label {
  background-color: #2d2d3a;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.status-toggle-label:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.dark-mode .status-toggle-label:hover {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.status-toggle-checkbox {
  margin-right: 12px;
  width: 20px;
  height: 20px;
  accent-color: #4caf50;
}

.dark-mode .status-toggle-checkbox {
  accent-color: #81c784;
}

.status-toggle-text {
  font-weight: 500;
  font-size: 16px;
  color: #333;
}

.dark-mode .status-toggle-text {
  color: #e9e9e9;
}

.status-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: bold;
  background-color: #f0f0f0;
}

.status-btn.active {
  color: white;
}

.status-btn:nth-child(1).active {
  background-color: #ff9800;
}

.status-btn:nth-child(2).active {
  background-color: #2196f3;
}

.status-btn:nth-child(3).active {
  background-color: #4caf50;
}

.task-detail-actions {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.dark-mode .task-detail-actions {
  border-top: 1px solid rgba(255, 255, 255, 0.05);
}

/* Forms */
.add-task-form,
.edit-task-form {
  background-color: #f9f9f9;
  border-radius: 16px;
  padding: 28px;
  margin-bottom: 25px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.dark-mode .add-task-form,
.dark-mode .edit-task-form {
  background-color: #2d2d3a;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 10px;
  font-weight: 500;
  color: #333;
  transition: color 0.3s ease;
  font-size: 0.95rem;
  letter-spacing: 0.3px;
}

.dark-mode .form-group label {
  color: #e9e9e9;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 10px 16px;
  border: 1px solid #ddd;
  border-radius: 12px;
  font-size: 14px;
  transition: all 0.3s ease;
  background-color: #fff;
  color: #333;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.form-group select.multi-select {
  height: auto;
  min-height: 120px;
}

.form-help-text {
  font-size: 12px;
  color: #666;
  margin-top: 5px;
  font-style: italic;
}

.dark-mode .form-help-text {
  color: #aaa;
}

.checkbox-group {
  display: flex;
  align-items: center;
}

.checkbox-group label {
  display: flex;
  align-items: center;
  cursor: pointer;
  margin-bottom: 0;
}

.checkbox-group input[type="checkbox"] {
  width: auto;
  margin-right: 8px;
  cursor: pointer;
}

/* Assign dropdown */
.assign-dropdown-container {
  position: relative;
  width: 100%;
}

.assign-dropdown-trigger {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 15px;
  background-color: #f5f5f5;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  color: #333;
}

.dark-mode .assign-dropdown-trigger {
  background-color: #2d2d3a;
  border-color: #444;
  color: #e0e0e0;
}

.assign-dropdown-trigger:hover {
  background-color: #f0f0f0;
  border-color: #d0d0d0;
}

.dark-mode .assign-dropdown-trigger:hover {
  background-color: #35354a;
  border-color: #555;
}

.assign-dropdown-trigger i {
  transition: transform 0.3s ease;
}

.member-list-container {
  position: absolute;
  top: calc(100% + 5px);
  left: 0;
  width: 100%;
  z-index: 100;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease;
  max-height: 0;
  overflow: hidden;
}

.dark-mode .member-list-container {
  background-color: #2d2d3a;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.member-list-container.active {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
  max-height: 250px;
  overflow-y: auto;
}

/* Member checkbox list */
.member-checkbox-list {
  display: flex;
  flex-direction: column;
  gap: 0;
  max-height: 200px;
  overflow-y: auto;
  padding: 0;
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}

.member-checkbox-list::-webkit-scrollbar {
  width: 6px;
}

.member-checkbox-list::-webkit-scrollbar-track {
  background: transparent;
}

.member-checkbox-list::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
}

.dark-mode .member-checkbox-list {
  scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
}

.dark-mode .member-checkbox-list::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.2);
}

.member-checkbox-item {
  padding: 6px 12px;
  transition: all 0.2s ease;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.member-checkbox-item:last-child {
  border-bottom: none;
}

.dark-mode .member-checkbox-item {
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.member-checkbox-item:hover {
  background-color: rgba(74, 108, 247, 0.05);
}

.dark-mode .member-checkbox-item:hover {
  background-color: rgba(90, 119, 255, 0.05);
}

.member-checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  width: 100%;
}

.member-checkbox-label input[type="checkbox"] {
  width: 10px;
  height: 10px;
  margin-right: 8px;
  cursor: pointer;
  accent-color: #4a6cf7;
}

.dark-mode .member-checkbox-label input[type="checkbox"] {
  accent-color: #5a77ff;
}

.member-name {
  font-weight: 400;
  font-size: 13px;
  transition: color 0.2s ease;
  color: #333;
}

.dark-mode .member-name {
  color: #e0e0e0;
}

.member-checkbox-label:hover .member-name {
  color: #4a6cf7;
}

.dark-mode .member-checkbox-label:hover .member-name {
  color: #5a77ff;
}

/* Assign to all option */
.assign-all-option {
  margin-top: 10px;
  padding: 8px 0;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.dark-mode .assign-all-option {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.dark-mode .form-group input,
.dark-mode .form-group textarea,
.dark-mode .form-group select {
  background-color: #35354a;
  border-color: #444;
  color: #e9e9e9;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
}

/* Datetime igroiunput styling for dark mode */
.dark-mode input[type="datetime-local"] {
  color-scheme: dark;
  background-color: #35354a;
  color: #e9e9e9;
  border-color: #444;
}

/* Calendar popup styling for dark mode */
.dark-mode ::-webkit-calendar-picker-indicator {
  filter: invert(1);
  background-color: transparent;
  cursor: pointer;
  opacity: 0.9;
  padding: 5px;
  border-radius: 3px;
}

/* Ensure calendar popup has dark background */
@supports (color-scheme: dark) {
  .dark-mode input[type="datetime-local"] {
    color-scheme: dark;
  }
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  border-color: #4a6cf7;
  outline: none;
  box-shadow: 0 0 0 3px rgba(74, 108, 247, 0.15);
  transform: translateY(-2px);
}

.dark-mode .form-group input:focus,
.dark-mode .form-group textarea:focus,
.dark-mode .form-group select:focus {
  border-color: #5a77ff;
  box-shadow: 0 0 0 3px rgba(90, 119, 255, 0.2);
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  margin-top: 25px;
}

.cancel-btn,
.submit-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.08);
  font-size: 0.95rem;
  letter-spacing: 0.3px;
}

.cancel-btn {
  background-color: #f0f0f0;
  color: #333;
}

.dark-mode .cancel-btn {
  background-color: #35354a;
  color: #e9e9e9;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
}

.cancel-btn:hover {
  background-color: #e0e0e0;
  transform: translateY(-3px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
}

.dark-mode .cancel-btn:hover {
  background-color: #3a3a4a;
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
}

.submit-btn {
  background-color: #4a6cf7;
  color: white;
  position: relative;
  overflow: hidden;
}

.submit-btn::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.submit-btn:hover::after {
  opacity: 1;
}

.dark-mode .submit-btn {
  background-color: #5a77ff;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
}

.submit-btn:hover {
  background-color: #385de0;
  transform: translateY(-3px);
  box-shadow: 0 6px 15px rgba(74, 108, 247, 0.25);
}

.dark-mode .submit-btn:hover {
  background-color: #4a66e0;
  box-shadow: 0 6px 15px rgba(90, 119, 255, 0.3);
}

.form-error {
  color: #f44336;
  margin-bottom: 15px;
  padding: 10px;
  background-color: #ffebee;
  border-radius: 4px;
}
