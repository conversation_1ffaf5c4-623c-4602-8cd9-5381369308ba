.urgency-selector {
  position: relative;
  display: inline-block;
}

.urgency-button {
  background: none;
  border: none;
  cursor: pointer;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  color: #2196f3;
  font-size: 1rem;
}

.urgency-button:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.dark-mode .urgency-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.urgency-dropdown {
  position: absolute;
  bottom: 45px;
  left: 0;
  width: 200px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
  z-index: 100;
  overflow: hidden;
  animation: fadeIn 0.2s ease;
}

.dark-mode .urgency-dropdown {
  background-color: #2d2d3a;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.urgency-dropdown-header {
  padding: 12px 15px;
  border-bottom: 1px solid #eee;
  font-weight: 500;
  color: #333;
  font-size: 0.9rem;
}

.dark-mode .urgency-dropdown-header {
  border-bottom: 1px solid #444;
  color: #e0e0e0;
}

.urgency-options {
  max-height: 200px;
  overflow-y: auto;
}

.urgency-option {
  padding: 10px 15px;
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.urgency-option:hover {
  background-color: #f5f5f5;
}

.dark-mode .urgency-option:hover {
  background-color: #3a3a48;
}

.urgency-option.selected {
  background-color: #f0f7ff;
}

.dark-mode .urgency-option.selected {
  background-color: #2a2a45;
}

.urgency-option i {
  width: 20px;
  text-align: center;
}

.urgency-option span {
  font-size: 0.9rem;
  color: #555;
}

.dark-mode .urgency-option span {
  color: #ddd;
}

/* Message urgency indicators */
.message-urgency {
  display: inline-flex;
  align-items: center;
  margin-left: 5px;
  font-size: 12px;
}

.message-urgency.low {
  color: #4caf50;
}

.message-urgency.high {
  color: #ff9800;
}

.message-urgency.urgent {
  color: #f44336;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

/* Notification styles for different urgency levels */
.notification-low {
  background-color: rgba(76, 175, 80, 0.1);
  border-left: 3px solid #4caf50;
}

.notification-normal {
  background-color: rgba(33, 150, 243, 0.1);
  border-left: 3px solid #2196f3;
}

.notification-high {
  background-color: rgba(255, 152, 0, 0.1);
  border-left: 3px solid #ff9800;
}

.notification-urgent {
  background-color: rgba(244, 67, 54, 0.1);
  border-left: 3px solid #f44336;
  animation: urgentPulse 2s infinite;
}

@keyframes urgentPulse {
  0% {
    border-left-color: #f44336;
  }
  50% {
    border-left-color: #ff8a80;
  }
  100% {
    border-left-color: #f44336;
  }
}

/* Dark mode notification styles */
.dark-mode .notification-low {
  background-color: rgba(76, 175, 80, 0.15);
}

.dark-mode .notification-normal {
  background-color: rgba(33, 150, 243, 0.15);
}

.dark-mode .notification-high {
  background-color: rgba(255, 152, 0, 0.15);
}

.dark-mode .notification-urgent {
  background-color: rgba(244, 67, 54, 0.15);
}
